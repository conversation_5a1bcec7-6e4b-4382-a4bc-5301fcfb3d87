import { _decorator, Component } from 'cc';
import { GuideData, GuideSignType, GuideType, UserGuide } from './UserGuide';

const { ccclass, property } = _decorator;

/**
 * 第二个主场景控制器
 * 负责管理第二个场景的逻辑和用户引导
 */
@ccclass('Main2')
export class Main2 extends Component {

    // 引导数据
    private guideData: GuideData = {
        nodesAndTexts: [
            {
                guideType: GuideType.TOUCH,
                path: "Canvas/Button",
                guideText: "这个按钮是Canvas的子节点，请点击~",
            },
            {
                guideType: GuideType.TOUCH,
                path: "Canvas/Button/Button-001",
                guideText: "这个按钮是左边按钮的子节点，请点击~",
            },
            {
                guideType: GuideType.ONLY_TEXT,
                guideText: "这就是新手引导插件~无需编写任何麻烦复杂的新手引导代码即可获得完整的新手引导效果~",
            }
        ],
        guideSignType: GuideSignType.FRAME
    };
    /**
     * 组件启动时调用
     * 开始显示用户引导
     */
    start(): void {
        // 获取用户引导组件并开始第一步引导
        let userGuide = this.node.getComponent(UserGuide);
        if (!userGuide) {
            userGuide = this.node.addComponent(UserGuide);
        }
        userGuide.setGuideData(this.guideData);
        userGuide.showGuide(1, true);
    }

    /**
     * 每帧更新
     * @param deltaTime 帧间隔时间
     */
    update(deltaTime: number): void {
        // 每帧更新逻辑
    }
}
