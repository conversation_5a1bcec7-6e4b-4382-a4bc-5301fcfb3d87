import { _decorator, CCInteger, Component, SortingLayers, UIRenderer } from 'cc';

const { ccclass, requireComponent, property, executeInEditMode, disallowMultiple } = _decorator;

/**
 * 2D排序设置接口
 */
interface ISorting2DSettings {
    sortingLayer: number;
    sortingOrder: number;
    sortingAsGroup: boolean;
    sortInGroup: boolean;
    ignoreSort: boolean;
    clone(): ISorting2DSettings;
}

/**
 * 默认的2D排序设置
 */
export const DEFAULT_SORTING2D: ISorting2DSettings = {
    sortingLayer: 0,
    sortingOrder: 0,
    sortingAsGroup: false,
    sortInGroup: true,
    ignoreSort: false,
    clone(): ISorting2DSettings {
        return {
            sortingLayer: this.sortingLayer,
            sortingOrder: this.sortingOrder,
            sortingAsGroup: this.sortingAsGroup,
            sortInGroup: this.sortInGroup,
            ignoreSort: this.ignoreSort,
            clone: this.clone
        };
    }
};

// 扩展 UIRenderer 类型定义
declare module 'cc' {
    interface UIRenderer {
        sorting2D: Sorting2D | null;
        useDefaultSort: boolean;
    }
}

/**
 * 2D排序组件
 */
@ccclass('Sorting2D')
@requireComponent(UIRenderer)
@executeInEditMode
@disallowMultiple
export class Sorting2D extends Component {
    @property({
        visible: false,
        serializable: true
    })
    private _sortingLayer: number = 0;

    @property({
        type: SortingLayers.Enum,
        tooltip: 'i18n:Sorting2D.sorting_layer_name'
    })
    get sortingLayerName(): number {
        return this._sortingLayer;
    }
    set sortingLayerName(value: number) {
        if (this._sortingLayer !== value) {
            this._sortingLayer = value;
        }
    }

    @property({
        visible: false,
        serializable: true
    })
    private _sortingOrder: number = 0;

    @property({
        type: CCInteger,
        tooltip: 'i18n:Sorting2D.sorting_order'
    })
    get sortingOrder(): number {
        return this._sortingOrder;
    }
    set sortingOrder(value: number) {
        this._sortingOrder = value;
    }

    @property({
        visible: false,
        serializable: true
    })
    private _sortingAsGroup: boolean = false;

    @property({
        tooltip: 'i18n:Sorting2D.sorting_as_group'
    })
    get sortingAsGroup(): boolean {
        return this._sortingAsGroup;
    }
    set sortingAsGroup(value: boolean) {
        this._sortingAsGroup = value;
    }

    @property({
        tooltip: 'i18n:Sorting2D.sort_in_group'
    })
    private _sortInGroup: boolean = false;

    get sortInGroup(): boolean {
        return this._sortInGroup;
    }
    set sortInGroup(value: boolean) {
        this._sortInGroup = value;
    }

    @property({
        tooltip: 'i18n:Sorting2D.ignore_sort'
    })
    private _ignoreSort: boolean = false;

    get ignoreSort(): boolean {
        return this._ignoreSort;
    }
    set ignoreSort(value: boolean) {
        this._ignoreSort = value;
    }

    private _renderer: UIRenderer | null = null;

    onLoad(): void {
        this._renderer = this.getComponent(UIRenderer);
    }

    onEnable(): void {
        if (this._renderer) {
            this._renderer.sorting2D = this;
            this._renderer.useDefaultSort = false;
        }
    }

    onDisable(): void {
        if (this._renderer) {
            this._renderer.useDefaultSort = true;
            this._renderer.sorting2D = null;
        }
    }
} 