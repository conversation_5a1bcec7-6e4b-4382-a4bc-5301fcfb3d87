import { Node } from "cc";
import { UIConfig } from "./Defines";
import { LayerPop } from "./LayerPop";
import { ViewParams } from "./LayerUI";

/** 
 * 模式弹窗参数接口
 * @description 用于存储模式弹窗的配置、参数和节点信息
 */
type DialogParam = {
    /** 界面配置 - 包含预制件路径、资源包名等信息 */
    config: UIConfig;
    /** 界面参数 - 包含界面ID和自定义参数 */
    params: { uiId: number, uiArgs: any };
    /** 界面节点 - 界面的实际节点对象 */
    node: Node | null
}

/**
 * <AUTHOR>
 * @data 2025-03-13 15:55
 * @filePath assets\core\manager\ui\LayerTop.ts
 * @description 顶层弹窗层 - 负责管理游戏中的模式弹窗界面
 * 支持弹窗队列管理，确保弹窗按顺序显示，一次只显示一个弹窗
 */
export class LayerTop extends LayerPop {
    /** 弹窗参数队列 - 存储待显示的弹窗参数 */
    private params: Array<DialogParam> = [];

    /**
     * 添加模式弹窗
     * @param config 界面配置
     * @param params 界面参数
     * @param node 界面节点
     * @description 将弹窗添加到队列中，如果当前没有显示的弹窗则立即显示
     */
    public add(config: UIConfig, params: { uiId: number, uiArgs: any }, node: Node | null): void {
        if (this.ui_nodes.size > 0) {
            this.params.push({ config: config, params: params, node: node });
            return;
        }
        this.show(config, params, node);
    }

    /**
     * 显示模式弹窗
     * @param config 界面配置
     * @param params 界面参数
     * @param node 界面节点
     * @description 显示指定的模式弹窗，优先使用缓存中的界面
     */
    private show(config: UIConfig, params: { uiId: number, uiArgs: any }, node: Node | null): void {
        // 检查缓存中是否存界面
        let vp: ViewParams = this.ui_cache.get(config.prefab) || { config: config, valid: false, node: null };
        vp.config = config;
        vp.valid = true;
        vp.node = node;
        this.ui_nodes.set(config.prefab, vp);

        this.showUi(vp, params);
    }

    /**
     * 窗口关闭事件处理
     * @param vp 视图参数
     * @description 关闭当前窗口后，显示队列中的下一个窗口
     */
    protected onCloseWindow(vp: ViewParams): void {
        super.onCloseWindow(vp);
        setTimeout(this.next.bind(this), 0);
    }

    /**
     * 显示下一个弹窗
     * @description 从队列中取出并显示下一个弹窗
     */
    private next(): void {
        if (this.params.length > 0) {
            let param = this.params.shift()!;
            this.show(param.config, param.params, param.node);
        }
    }

    /**
     * 设置触摸事件阻挡
     * @description 当所有弹窗关闭且队列为空时，禁用点击阻挡
     */
    protected setBlackDisable(): void {
        if (this.params.length == 0) {
            this.black.enabled = false;
            this.closeVacancyRemove();
            this.closeMask()
        }
    }
}