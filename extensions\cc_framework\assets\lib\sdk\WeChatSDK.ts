import 'minigame-api-typings';
import { Debug } from '../../core/logger/Debug';

export class WeChatSDK {
    /** 登录 */
    public static Login(successCallback: (data: WechatMinigame.LoginSuccessCallbackResult) => void, failCallback: (data: WechatMinigame.GeneralCallbackResult) => void): void {
        wx.login({
            success: (res) => {
                if (res.code) {
                    successCallback(res);
                } else {
                    Debug.error("login success but code is null:", res);
                    failCallback(res);
                }
            },
            fail: (res) => {
                Debug.error("login fail:", res);
                failCallback(res);
            }
        })
    }
    /**
     * 用户信息获取
     * @param style 样式
     * @param successCallback 成功回调
     * @param failCallback 失败回调
     */
    public static GetUserInfo(style: { image: string, left: number, top: number, width: number, height: number }, successCallback: (data: WechatMinigame.GetUserInfoSuccessCallbackResult) => void, failCallback: (data: WechatMinigame.GeneralCallbackResult) => void): void {
        const login = function () {
            wx.getUserInfo({
                withCredentials: true,
                success: (res) => {
                    // 已经授权，直接获取用户信息
                    Debug.log("getUserInfo success:", res);
                    successCallback(res);
                },
                fail: (res) => {
                    Debug.error("getUserInfo fail:", res);
                    failCallback(res);
                }
            });
        }
        wx.getSetting({
            success(res) {
                if (res.authSetting['scope.userInfo'] === true) {
                    login();
                } else {
                    Debug.log("getUserSetting success:", res);
                    const button = wx.createUserInfoButton({
                        type: "image",
                        image: style.image,
                        style: {
                            left: style.left,
                            top: style.top,
                            width: style.width,
                            height: style.height,
                            backgroundColor: "rgb(255, 255, 255)",
                        },
                    });
                    button.onTap((res) => {
                        if (res.errMsg.indexOf(':ok') > -1 && !!res.rawData) {
                            // 获取用户信息
                            login();
                            button.destroy();
                        } else {
                            Debug.error("getUserSetting fail:", res);
                            WeChatSDK.requestPrivacyAuthorize(failCallback);
                        }
                    });
                }
            },
            fail(res) {
                Debug.error("getUserSetting fail:", res);
                failCallback(res);
            }
        });
    }
    /** 请求隐私授权 */
    private static requestPrivacyAuthorize(failCallback: (data: WechatMinigame.GeneralCallbackResult) => void): void {
        wx.requirePrivacyAuthorize({
            success: (res) => {
                Debug.log("requestPrivacyAuthorize success:", res);
            },
            fail: (res) => {
                Debug.error("requestPrivacyAuthorize fail:", res);
                failCallback(res);
            }
        });
    }

    /** 拨打电话 */
    public static CallPhone(phone: string): void {
        // wx.makePhoneCall({
        //     phoneNumber: phone,
        // });
    }
    /** 客服会话 */
    public static CustomerService(): void {
        wx.openCustomerServiceConversation({
            success(res) {
                Debug.log("客服会话打开成功");
            },
            fail(err) {
                Debug.error("客服会话打开失败", err);
            }
        });
    }
    /** 
     * 跳转小程序
     * @param appId 小程序appid
     * @param path 小程序路径 默认空
     * @param extraData 额外数据 默认空
     * @param envVersion 环境版本 默认release
     */
    public static JumpMiniProgram(appId: string, path: string = "", extraData: Object | undefined = undefined, envVersion: 'develop' | 'trial' | 'release' = 'release'): void {
        wx.navigateToMiniProgram({
            appId: appId, // 必填
            path: path, // 可选，跳转路径
            extraData: extraData,
            envVersion: envVersion, // 可选，环境版本（develop/trial/release）
            success(res) {
                Debug.log("跳转成功");
            },
            fail(err) {
                Debug.error("跳转失败", err);
            }
        });
    }
}