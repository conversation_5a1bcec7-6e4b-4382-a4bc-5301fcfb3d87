import { _decorator, AudioSource } from 'cc';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 17:15
 * @filePath assets\core\manager\audio\AudioEffect.ts
 * @description 音效管理器
 */
@ccclass('AudioEffect')
export class AudioEffect extends AudioSource {
    /** 背景音乐播放完成回调 */
    public onComplete: Function | null = null;
    protected start(): void {
        this.node.on(AudioSource.EventType.ENDED, this.onAudioEnded, this);
    }

    private onAudioEnded(): void {
        this.onComplete && this.onComplete();
    }

}