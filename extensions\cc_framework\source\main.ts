import AdmZip from 'adm-zip';
import { existsSync, mkdirSync } from 'fs';
import { join } from 'path';

/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
export const methods: { [key: string]: (...any: any) => any } = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    showLog() {
        loadNecessaryResource();
        console.log('Hello World');
    },
};

/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
export function load() {
    loadNecessaryResource();
}

function loadNecessaryResource() {
    console.log("[Framework] 加载插件必要资源 start--------");
    try {
        // 构建当前项目的resources路径
        const currentResourcesPath = join(Editor.Project.path, 'assets', 'resources');
        // 插件必要资源目录
        const frameResourcesPath = join(__dirname, "..");
        
        // 确保目标目录存在
        if (!existsSync(currentResourcesPath)) {
            mkdirSync(currentResourcesPath, { recursive: true });
        }

        // 检查resources.zip是否存在
        const zipPath = join(frameResourcesPath, 'resources.zip');
        if (existsSync(zipPath)) {
            console.log('[Framework] 检查resources.zip中的文件...');
            const zip = new AdmZip(zipPath);
            const zipEntries = zip.getEntries();
            
            // 检查是否需要解压
            let needExtract = false;
            
            // 检查每个文件是否存在
            for (const entry of zipEntries) {
                if (!entry.isDirectory) {
                    const targetPath = join(currentResourcesPath, entry.entryName);
                    if (!existsSync(targetPath)) {
                        needExtract = true;
                        console.log(`[Framework] 文件不存在: ${entry.entryName}`);
                        break;
                    }
                }
            }

            // 如果需要解压，则解压所有文件
            if (needExtract) {
                console.log('[Framework] 开始解压resources.zip...');
                zip.extractAllTo(currentResourcesPath, true);
                console.log('[Framework] resources.zip解压完成');
            } else {
                console.log('[Framework] 所有文件已存在，无需解压');
            }
        } else {
            console.log('[Framework] resources.zip文件不存在');
        }

        console.log("[Framework] 加载插件必要资源 end--------");
    } catch (error) {
        console.error('[Framework] 文件处理过程中发生错误:', error);
    }
}

/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
export function unload() { }
