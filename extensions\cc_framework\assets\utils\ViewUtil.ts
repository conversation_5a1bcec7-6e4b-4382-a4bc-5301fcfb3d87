import { __private, Animation, AnimationClip, AssetManager, Camera, Component, EventTouch, instantiate, js, Node, Prefab, UITransform, v3, Vec2, Vec3 } from "cc";
import { APP } from "../core/APP";
import { Debug } from "../core/logger/Debug";

/**
 * 视图工具类
 * @description 提供视图相关的工具方法
 * 包括预制体创建、组件获取、坐标转换、动画控制等功能
 * 支持同步和异步的资源加载，以及节点间的坐标空间转换
 */
export class ViewUtil {
    /**
     * 创建预制体节点
     * @description 从指定路径加载预制体并实例化,如果资源未加载会自动加载
     * @param path 预制体资源路径
     * @param bundleName 资源包名称，默认为resources
     * @returns 实例化后的节点，如果加载失败则返回null
     */
    public static createPrefabNode(path: string, callback: (p: Node) => void, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): void {
        const p = APP.res.get(path, Prefab, bundleName);
        if (p) {
            callback(instantiate(p));
        } else {
            APP.res.loadAsync(bundleName, path, Prefab).then(p => callback(instantiate(p)));
        }
    }

    /**
     * 异步加载预制体并创建节点
     * @description 异步加载预制体资源并实例化，建议使用GameComponent中的同名方法以自动管理内存释放,如果资源未加载会自动加载
     * @param path 预制体资源路径
     * @param bundleName 资源包名称，默认为resources
     * @returns Promise<Node> 实例化后的节点
     */
    public static createPrefabNodeAsync(path: string, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): Promise<Node> {
        return new Promise(resolve => this.createPrefabNode(path, p => resolve(p), bundleName));
    }

    /**
     * 从预制体中获取组件
     * @description 加载预制体并获取指定类型的组件
     * @param prefabPath 预制体路径
     * @param comp 组件类型
     * @param bundleName 资源包名称，默认为resources
     * @returns 获取到的组件，如果加载失败则返回null
     */
    public static getComponentFormPrefab<T extends Component>(prefabPath: string, comp: __private.__types_globals__Constructor<T>, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): T | null {
        Debug.error(js.getClassName(this), "getComponentFormPrefab需要重构");
        // const node = this.createPrefabNode(prefabPath, bundleName);
        // return node ? node.getComponent(comp) : null;
        // const node = 
        return null;
    }

    /**
     * 节点间坐标转换
     * @description 将A节点空间中的坐标转换为B节点空间中的坐标
     * @param a A节点
     * @param b B节点
     * @param aPos A节点空间中的相对位置
     * @returns B节点空间中的坐标
     */
    public static calculateASpaceToBSpacePos(a: Node, b: Node, aPos: Vec3): Vec3 {
        const world: Vec3 = a.getComponent(UITransform)!.convertToWorldSpaceAR(aPos);
        return b.getComponent(UITransform)!.convertToNodeSpaceAR(world);
    }

    /**
     * 屏幕坐标转节点空间坐标
     * @description 将触摸事件的屏幕坐标转换为指定节点空间中的坐标
     * @param event 触摸事件
     * @param space 目标节点空间
     * @returns 节点空间中的坐标
     */
    public static calculateScreenPosToSpacePos(event: EventTouch, space: Node): Vec3 {
        const uil = event.getUILocation();
        const worldPos: Vec3 = v3(uil.x, uil.y);
        return space.getComponent(UITransform)!.convertToNodeSpaceAR(worldPos);
    }

    /**
     * 添加节点动画
     * @description 为节点添加动画组件并播放指定动画
     * @param clip 动画剪辑
     * @param node 目标节点
     * @param onlyOne 是否只允许播放一个动画，默认为true
     * @param isDefaultClip 是否设置为默认动画剪辑，默认为false
     */
    public static addNodeAnimation(clip: AnimationClip, node: Node, onlyOne: boolean = true, isDefaultClip: boolean = false): void {
        if (!node || !node.isValid) {
            return;
        }

        let anim = node.getComponent(Animation);
        if (anim == null) {
            anim = node.addComponent(Animation);
        }

        if (onlyOne && anim.getState(clip.name) && anim.getState(clip.name).isPlaying) {
            return;
        }

        if (isDefaultClip) {
            anim.defaultClip = clip;
            anim.play();
            return;
        }

        // 播放完成后恢复播放默认动画
        anim.once(Animation.EventType.FINISHED, () => {
            if (anim!.defaultClip) {
                anim!.play();
            }
        }, this);

        if (anim.getState(clip.name)) {
            anim.play(clip.name);
            return
        }
        anim.createState(clip, clip!.name);
        anim.play(clip!.name);
    }

    /**
     * 播放动画
     * @description 加载并播放指定路径的动画，播放完成后自动销毁节点
     * @param path 动画资源路径
     * @param bundleName 资源包名称，默认为resources
     * @returns 动画组件，如果加载失败则返回null
     */
    public static PlayAnimation(path: string, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): Animation | null {
        const ani = this.getComponentFormPrefab(path, Animation, bundleName);
        if (ani) ani.once(Animation.EventType.FINISHED, () => ani.node.destroy());
        return ani;
    }

    /**
     * 获取屏幕坐标
     * @description 将节点或世界坐标转换为屏幕坐标
     * 目前仅支持UI视图位置的转换，场景坐标转换需要扩展
     * @param target 目标节点或世界坐标
     * @param out 输出位置对象
     * @param camera 可选的相机组件
     * @returns 屏幕坐标
     */
    public static getScreenPosition(target: Node | Vec3, out?: Vec2, camera?: Camera): Vec2 {
        if (!out) { out = new Vec2(); }
        // if (target instanceof Node) { target = target.getWorldPosition(); }
        // let sysInfo = wx.getSystemInfoSync(), stage = view.getVisibleSizeInPixel();
        // // ui视图的转换
        // let screenPos;
        // if (camera) {
        //     screenPos = camera.worldToScreen(target);
        // } else {
        //     screenPos = app.ui.camera.worldToScreen(target);
        // }
        // out.x = screenPos.x * sysInfo.windowWidth / stage.width;
        // out.y = (stage.height - screenPos.y) * sysInfo.windowHeight / stage.height;
        Debug.warn(js.getClassName(this), "getScreenPosition需要重构");
        return out;
    }
}