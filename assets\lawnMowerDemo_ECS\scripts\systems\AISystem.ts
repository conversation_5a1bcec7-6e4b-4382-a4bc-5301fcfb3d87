import { Vec2 } from "cc";
import { Component } from "db://framework/ECS/Component";
import { Entity } from "db://framework/ECS/Entity";
import { EntitySystem } from "db://framework/ECS/systems/EntitySystem";
import { Matcher } from "db://framework/ECS/utils/Matcher";
import { Movement } from "../components/Movement";
import { Transform } from "../components/Transform";
import { EntityTags } from "../EntityTags";

/**
 * <AUTHOR>
 * @data 2025-08-04 10:28
 * @filePath assets\lawnMowerDemo_ECS\scripts\systems\AISystem.ts
 * @description AI组件 - 标记需要AI控制的实体
 */
export class AIComponent extends Component {
    private _aiType: string = 'chaser'; // 'chaser', 'patrol', 'guard' 等
    constructor(aiType: string = 'chaser') {
        super();
        this._aiType = aiType;
    }

}
/**
 * <AUTHOR>
 * @data 2025-08-04 10:26
 * @filePath assets\lawnMowerDemo_ECS\scripts\systems\AISystem.ts
 * @description AI系统 - 处理敌人AI行为
 */
export class AISystem extends EntitySystem {

    constructor() {
        super(Matcher.empty().all(Transform, Movement, AIComponent));
    }

    protected process(entities: Entity[]): void {
        const players = this.findPlayerEntities();

        for (const entity of entities) {
            const transform = entity.getComponent(Transform);
            const movement = entity.getComponent(Movement);
            const ai = entity.getComponent(AIComponent);

            if (!transform || !movement || !ai) continue;

            this.updateChaserAI(entity, transform, movement, players);
        }
    }
    /**
     * 更新追击者AI
     */
    private updateChaserAI(entity: Entity, transform: Transform, movement: Movement, players: Entity[]): void {
        if (players.length === 0) {
            movement.inputDirection.set(0, 0);
            return;
        }

        const nearestPlayer = this.findNearestTarget(transform, players);

        if (nearestPlayer) {
            const playerTransform = nearestPlayer.getComponent(Transform);
            if (playerTransform) {
                const direction = new Vec2(
                    playerTransform.position.x - transform.position.x,
                    playerTransform.position.y - transform.position.y
                );

                const distance = direction.length();
                if (distance > 20) {
                    direction.normalize();
                    movement.inputDirection.set(direction.x, direction.y);
                } else {
                    movement.inputDirection.set(0, 0);
                }
            }
        } else {
            movement.inputDirection.set(0, 0);
        }
    }
    /** 寻找最近的目标 */
    private findNearestTarget(transform: Transform, targets: Entity[]): Entity | null {
        let nearestTarget = null;
        let nearestDistance = Infinity;

        for (const target of targets) {
            const targetTransform = target.getComponent(Transform);
            if (!targetTransform) continue;

            const distance = Vec2.distance(
                new Vec2(transform.position.x, transform.position.y),
                new Vec2(targetTransform.position.x, targetTransform.position.y)
            );

            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestTarget = target;
            }
        }

        return nearestTarget;
    }
    private findPlayerEntities(): Entity[] {
        return this.scene.findEntitiesByTag(EntityTags.PLAYER);
    }

}