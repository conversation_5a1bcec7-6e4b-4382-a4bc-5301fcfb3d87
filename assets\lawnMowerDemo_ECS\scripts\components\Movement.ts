import { Vec2 } from "cc";
import { Component } from "db://framework/ECS/Component";

/**
 * <AUTHOR>
 * @data 2025-08-02 14:55
 * @filePath assets\lawnMowerDemo_ECS\scripts\components\Movement.ts
 * @description 移动组件 - 控制实体的移动行为
 */
export class Movement extends Component {

    private _maxSpeed: number;
    /** 最大移动速度 */
    public get maxSpeed(): number { return this._maxSpeed; }
    // public set maxSpeed(value: number) { this._maxSpeed = value; }
    private _inputDirection: Vec2 = new Vec2();
    /** 目标移动方向 */
    public get inputDirection(): Vec2 { return this._inputDirection; }
    // public set inputDirection(value: Vec2) { this._inputDirection = value; }
    private _velocity: Vec2 = new Vec2();
    public get velocity(): Vec2 { return this._velocity; }
    // public set velocity(value: Vec2) { this._velocity = value; }
    constructor(maxSpeed: number = 100) {
        super();
        this._maxSpeed = maxSpeed;
    }

}