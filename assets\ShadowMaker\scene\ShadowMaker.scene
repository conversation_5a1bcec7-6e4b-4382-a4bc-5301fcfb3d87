[{"__type__": "cc.SceneAsset", "_name": "ShadowMaker", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "ShadowMaker", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 124}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 125}, "_id": "59d44f57-43cd-4616-83da-7a31a517a15b"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 42}, {"__id__": 65}, {"__id__": 95}, {"__id__": 111}], "_active": true, "_components": [{"__id__": 120}, {"__id__": 121}, {"__id__": 122}, {"__id__": 123}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 640, "y": 360, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 370.2429906542056, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 110, "g": 74, "b": 155, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_name": "ndShadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "21hiBOty9HSpiRwAeWF0rC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dcEJea5gNBI7oyX1Gqjbyc"}, {"__type__": "cc.Node", "_name": "Panels", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 8}, {"__id__": 16}, {"__id__": 24}, {"__id__": 32}], "_active": true, "_components": [{"__id__": 40}, {"__id__": 41}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 278.558, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "29v0K/cFRDVaHW86ARb1Lp"}, {"__type__": "cc.Node", "_name": "RotationButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [{"__id__": 9}], "_active": true, "_components": [{"__id__": 12}, {"__id__": 13}, {"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7727RqAb9J07pLA6FKsAo/"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 8}, "_children": [], "_active": true, "_components": [{"__id__": 10}, {"__id__": 11}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "27wRUIaFBJu6uFESwKCSQ3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 133.3203125, "height": 90.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5a6xkjauRDKqG4tX0kQ2sO"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "拖影旋转\n(使用预制阴影)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "0fcgVHMc5LE6dhZyeMIGXc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a03zt8agxDNbD3owi31lgl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "6evanDwqFLYrR47lXOE4AR"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 15}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 8}, "_id": "f2eQgDYQFCcoNx/g/g0hTO"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "c8120IKFL9JrpRpuonMC0pE", "handler": "onButtonClick", "customEventData": "1"}, {"__type__": "cc.Node", "_name": "MovingButton", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [{"__id__": 17}], "_active": true, "_components": [{"__id__": 20}, {"__id__": 21}, {"__id__": 22}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -125, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f5oSnaEQxGx5TU2lrg0QIH"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 18}, {"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "55/M9wowlGv4IVsH7Z86pX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 133.3203125, "height": 90.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d5+roGIclEj5OPv1j3FQAH"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "拖影移动\n(使用预制阴影)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "2752prNrFAjqBSbnvJ044p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b5IjgPdc5JrL645erro1Qt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "03WzxgUaBMHamIpPvLFhEr"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 23}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 16}, "_id": "80JjEDL1RKd43zEhh2k+Ae"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "c8120IKFL9JrpRpuonMC0pE", "handler": "onButtonClick", "customEventData": "2"}, {"__type__": "cc.Node", "_name": "MovingButton-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [{"__id__": 25}], "_active": true, "_components": [{"__id__": 28}, {"__id__": 29}, {"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 125, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "65sD8A5FxGeJeO8nFZc03E"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 27}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b8eeboP4BFH7lL5w+xSjOT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 153.3203125, "height": 90.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "235pNfIqpPwYOB2pYgL85H"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "自定义拖影运动\n(使用自定义阴影)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "04MmxNpKhKu5hQ4srZoXJn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7bzAObYxZKTr7r9grq5hWk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "24VPp1o2RCfaW8oX321TlQ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 31}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 24}, "_id": "53G4Fw7iFMS56wKNcuO8Bv"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "c8120IKFL9JrpRpuonMC0pE", "handler": "onButtonClick", "customEventData": "3"}, {"__type__": "cc.Node", "_name": "MovingButton-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 7}, "_children": [{"__id__": 33}], "_active": true, "_components": [{"__id__": 36}, {"__id__": 37}, {"__id__": 38}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "35iTQKxDZD8a5kB+RHfyqi"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 35}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "85fyC/705LqLK7518X93i7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 153.3203125, "height": 90.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "72rjyzb4NJdIcsEJpaxXOJ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "Spine动画拖影\n(使用自定义阴影)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "3789sWXZ1MgYMwWySNNFFE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "2aMjHZduRCfIHCjqOo2DOf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "84BaWA0r1Gk6abygVQmImR"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 39}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 32}, "_id": "1beKbv1kVGhJmysMzczycF"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 2}, "component": "", "_componentId": "c8120IKFL9JrpRpuonMC0pE", "handler": "onButtonClick", "customEventData": "4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 950, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "15zZY+UIJLyKGiF+tGmXQs"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 7}, "_enabled": false, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 50, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": "86im0+hdJF+Y4KT8nETER8"}, {"__type__": "cc.Node", "_name": "Rotation", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 43}, {"__id__": 50}, {"__id__": 57}], "_active": false, "_components": [{"__id__": 64}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0fY0APswdHHpXf7HWf5fCQ"}, {"__type__": "cc.Node", "_name": "sword-rotate-ellipse-shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [{"__id__": 44}], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -336.662, "y": -135.007, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eeZBIO3rlJWZ1EeOO6Z489"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 43}, "_children": [], "_active": true, "_components": [{"__id__": 45}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 89.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "021krd/StHc7Q+iNAS2/J9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "406MWqVTlAR6gzNljAFLkj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "59TBeXCdVAV7V3hKJyflxt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "35EwzXt8xDgLQAD3cfidpG"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "09e7e5d1-0e93-4025-bab2-e2267dff0fdb", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 44}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "1cN+WQKG1E37s0jrHHFn15"}, {"__type__": "42b5d6iJ75Krq935RFtzMZi", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": null, "isClockwise": true, "rotationSpeed": 5, "_id": "bchQFD5YdOHYSibceOXQZW"}, {"__type__": "cc.Node", "_name": "sword-rotate-ellipse-shadow-inverse", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [{"__id__": 51}], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}, {"__id__": 55}, {"__id__": 56}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 299.066, "y": -135.007, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f8TsS/dslKBK9/3I2x5ZxD"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 50}, "_children": [], "_active": true, "_components": [{"__id__": 52}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.524, "y": 89.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6dg2LB3/JFlp+SgtCwp/40"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 51}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d2M5ErhXtI8bZbvGpSNl9/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "b0A/DskutCsYfRhrWQ49iW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "521jQacidAsaNbv7lwjr+f"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "09e7e5d1-0e93-4025-bab2-e2267dff0fdb", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 51}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "02otMGJRJMjpJxn9etAshJ"}, {"__type__": "42b5d6iJ75Krq935RFtzMZi", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": null, "isClockwise": false, "rotationSpeed": 5, "_id": "ce9DXdopxK7KjdbdGIUmzu"}, {"__type__": "cc.Node", "_name": "sword-rotate-ellipse-shadow-inverse-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 42}, "_children": [{"__id__": 58}], "_active": true, "_components": [{"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -16.653, "y": -135.007, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0aTXSJN8lDrrxyRrTHeBUZ"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 57}, "_children": [], "_active": true, "_components": [{"__id__": 59}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -0.524, "y": 89.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "985j/r9htN0Z0ENItwWGWw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e8LT5Wj6xEBb1zhYXa9A9f"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "30jIW9ealFxJbmhe3AZV89"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "78srKkMM1Grp4ByksiXwxI"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "9f860512-64e9-4ec3-8503-918cef993761", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 58}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "e91Fjj0O5Ds45mFsfpMmFR"}, {"__type__": "42b5d6iJ75Krq935RFtzMZi", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": null, "isClockwise": false, "rotationSpeed": 5, "_id": "a31Jne76lHQ6SDPC6AS5ZY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 42}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7dSAZHKyRKHK/s27hS0zaw"}, {"__type__": "cc.Node", "_name": "Moving", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 66}, {"__id__": 73}, {"__id__": 80}, {"__id__": 87}], "_active": false, "_components": [{"__id__": 94}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9c1h1C6txGL6HqlM6TFfP/"}, {"__type__": "cc.Node", "_name": "sword-moving-ellipse-shadow-verticle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 65}, "_children": [{"__id__": 67}], "_active": true, "_components": [{"__id__": 69}, {"__id__": 70}, {"__id__": 71}, {"__id__": 72}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 73.772, "y": -244.581, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "89MOn9fvpMa4ZL2QD4yn3s"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [{"__id__": 68}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 13.356, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "90rINH1CpOAqaxuEpAl34E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a8aA3PSHRGULgGCWMXAkP3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "799Hab1MNLMYk3oXtkExKq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d8u4/Z4ydFsIJinkErDpMM"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "09e7e5d1-0e93-4025-bab2-e2267dff0fdb", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 67}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "c3oGig73VKcKW41lbcywaX"}, {"__type__": "70e5alBfTZBG6hDTtoFQWg0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 66}, "_enabled": true, "__prefab": null, "isBottomTop": true, "moveSpeed": 5, "_id": "c5BnL09K5N9otZTgpJXrM0"}, {"__type__": "cc.Node", "_name": "sword-moving-ellipse-shadow-verticle-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 65}, "_children": [{"__id__": 74}], "_active": true, "_components": [{"__id__": 76}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 385.777, "y": -315.153, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "abc98/QE5JhIDvACg+FfKr"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 73}, "_children": [], "_active": true, "_components": [{"__id__": 75}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 13.356, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cbkn6pTgJJe4kL4g1hwoCl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 74}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1da5nBoOJKqr0Q6R+Ua1Ry"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "660e1Rds5ImJ9iKmKGnpPS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e9pqnbYq5KnK1eRiLvYo2H"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "09e7e5d1-0e93-4025-bab2-e2267dff0fdb", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 74}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "1el4n5eMZJGJxVSTWX5QuM"}, {"__type__": "70e5alBfTZBG6hDTtoFQWg0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 73}, "_enabled": true, "__prefab": null, "isBottomTop": true, "moveSpeed": 5, "_id": "75AVrF/FZN3pWrJOnaQgmY"}, {"__type__": "cc.Node", "_name": "sword-moving-ellipse-shadow-horizontal", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 65}, "_children": [{"__id__": 81}], "_active": true, "_components": [{"__id__": 83}, {"__id__": 84}, {"__id__": 85}, {"__id__": 86}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -451.807, "y": 56.281, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": "7bsLzbjAhHybLgbx+wZSZn"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 80}, "_children": [], "_active": true, "_components": [{"__id__": 82}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 13.356, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b80DULvuRNfqBCAdX0B23v"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f80Vdavy1KjLcidZ/uHIcH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "c0Pa0ioGVOrowS4EQ6iXcs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "7dSPzqRqtOoYP7QxsfPo3o"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "09e7e5d1-0e93-4025-bab2-e2267dff0fdb", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 81}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "265urns5hNhpHTPRUTDG3g"}, {"__type__": "70e5alBfTZBG6hDTtoFQWg0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 80}, "_enabled": true, "__prefab": null, "isBottomTop": false, "moveSpeed": 5, "_id": "86i6XYg3tFB78DBPmwdbhf"}, {"__type__": "cc.Node", "_name": "sword-moving-ellipse-shadow-horizontal-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 65}, "_children": [{"__id__": 88}], "_active": true, "_components": [{"__id__": 90}, {"__id__": 91}, {"__id__": 92}, {"__id__": 93}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -604.095, "y": -142.436, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": "afBGKt6SNECIrRdcUGUHBu"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 87}, "_children": [], "_active": true, "_components": [{"__id__": 89}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 13.356, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eeZcilxTFO0IcHwquxk7Nt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "38h5WxhSVPD426Dd0X/air"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "51YAza22hLIZRi7GCsGHBz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "b2PqikEahHT7tkLgiYNvbN"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "09e7e5d1-0e93-4025-bab2-e2267dff0fdb", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 88}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "28epndFh1MsJNg6RLea4ru"}, {"__type__": "70e5alBfTZBG6hDTtoFQWg0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "isBottomTop": false, "moveSpeed": 5, "_id": "832v0dmMpNXZNMdg9Jtzwr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b1xT5E6v5KbbV3atrKHzId"}, {"__type__": "cc.Node", "_name": "Custom", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 96}, {"__id__": 103}], "_active": false, "_components": [{"__id__": 110}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2aEkyPvZhJL7knetydR2iL"}, {"__type__": "cc.Node", "_name": "sword-rotate-custom-shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 95}, "_children": [{"__id__": 97}], "_active": true, "_components": [{"__id__": 99}, {"__id__": 100}, {"__id__": 101}, {"__id__": 102}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 155.487, "y": -84.864, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "acd+nlsuFJeZjjItk3Dq3a"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 96}, "_children": [], "_active": true, "_components": [{"__id__": 98}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 58.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c59qYiP1pIxoW2JorGAqi7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 97}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "02Wm8E5cFG5L/x1BVbbNRv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "7eWDaIivdPWIBFyTs/ylvj"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "aaohqJcNRMho1x2OV0eOf0"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "48e4f1a4-95b8-4d99-a0fd-2f9e485a5247", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 97}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "7eJjoZg7ZOMp9LqmzAIUiB"}, {"__type__": "42b5d6iJ75Krq935RFtzMZi", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": null, "isClockwise": true, "rotationSpeed": 5, "_id": "c9xDGan9RNmLt0ioS8nV2j"}, {"__type__": "cc.Node", "_name": "sword-move-custom-shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 95}, "_children": [{"__id__": 104}], "_active": true, "_components": [{"__id__": 106}, {"__id__": 107}, {"__id__": 108}, {"__id__": 109}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -576.238, "y": 11.709, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": "6dhjudtBdE0pLUR4opoKs1"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 103}, "_children": [], "_active": true, "_components": [{"__id__": 105}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 58.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "62jhasVndNhJhInWwVCPmm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 104}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8fotqP39dF6p3/ALu9Z3VL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 38, "height": 117}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": "4193DjFvtAwLwRWOalpqPZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25c3502b-b70c-4652-acd1-c928f0839942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "eah2Dmuh1FPYRFR8tvllQ2"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": null, "trailInterval": 0.01, "trailDuration": 0.5, "trailStartScale": 1, "trailEndScale": 0.8, "trailStartOpacity": 200, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "48e4f1a4-95b8-4d99-a0fd-2f9e485a5247", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 104}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "eeKZ51CzxAvYQbBx0uDQpw"}, {"__type__": "70e5alBfTZBG6hDTtoFQWg0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": null, "isBottomTop": false, "moveSpeed": 5, "_id": "f1cqYtSSVN3ZrqNY+E2Lwp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "feFZ/OCfpCUo9pfcZminof"}, {"__type__": "cc.Node", "_name": "Spine", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 112}], "_active": true, "_components": [{"__id__": 119}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0fUIBCbP1D2IrPYpnXZ1w3"}, {"__type__": "cc.Node", "_name": "spineboy-pro", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 111}, "_children": [{"__id__": 113}], "_active": true, "_components": [{"__id__": 115}, {"__id__": 116}, {"__id__": 117}, {"__id__": 118}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -495.865, "y": -289.718, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5, "y": 0.5, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3abf88PtdDgJCE4S/6Yes9"}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 112}, "_children": [], "_active": true, "_components": [{"__id__": 114}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "05x1EdQUpMoqQfdXz1yUF/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f9Le8jIglJm5OQ0zcCYN/e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 418.45001220703125, "height": 686.2000122070312}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.4507826487754681, "y": 0.01157097044006596}, "_id": "69h0bkl3FOsrN5CjmFEjmz"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "11a6c8a1-2e2e-4c38-928e-decfcedaed3e", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "run", "_premultipliedAlpha": true, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": "4a263z1XFJnY+/iYY3PNZL"}, {"__type__": "70e5alBfTZBG6hDTtoFQWg0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "isBottomTop": false, "moveSpeed": 5, "_id": "c2dsRiuHFH47vSGCqMPxMB"}, {"__type__": "d3f08wToB5LGpQ5LRYLPx6x", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": null, "trailInterval": 0.2, "trailDuration": 0.8, "trailStartScale": 0.5, "trailEndScale": 0.45, "trailStartOpacity": 50, "trailEndOpacity": 0, "shadowPrefab": {"__uuid__": "dd846a0f-6f36-4f6e-b406-57f6a1e85cbd", "__expectedType__": "cc.Prefab"}, "shadowPosition": {"__id__": 113}, "easing": "fade", "ndShadowParent": {"__id__": 5}, "_id": "77zx4bHIRLnJhgZ+I0wD8j"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5bDdVN6alLQqLAMmaEahYd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "c8120IKFL9JrpRpuonMC0pE", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "ndRotation": {"__id__": 42}, "ndMove": {"__id__": 65}, "ndCustom": {"__id__": 95}, "ndSpine": {"__id__": 111}, "_id": "ffRdTQ+j5HyIApEOpuG+65"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "59d44f57-43cd-4616-83da-7a31a517a15b", "instance": null, "targetOverrides": null}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 126}, "shadows": {"__id__": 127}, "_skybox": {"__id__": 128}, "fog": {"__id__": 129}, "octree": {"__id__": 130}, "skin": {"__id__": 131}, "lightProbeInfo": {"__id__": 132}, "postSettings": {"__id__": 133}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]