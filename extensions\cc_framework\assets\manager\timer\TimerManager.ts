import { _decorator, game, math } from 'cc';
import { BaseManager } from '../../base/BaseManager';
import { StringUtil } from '../../utils/StringUtil';
import { Timer } from './Timer';
const { ccclass, property } = _decorator;

interface TimerData {
    id: string;
    /** 定时触发组件 */
    timer: Timer,
    /** 管理对象 */
    object: any,
    /** 时间字段 */
    field: string | number,
    /** 每秒事件 */
    onSecond: Function | undefined,
    /** 倒计时完成事件 */
    onComplete: Function | undefined,
    /** 触发事件的对象 */
    target: object | null,
    /** 开始时间 */
    startTime: number | null,
    /** 计时器总时间 */
    totalTime: number,
}
/**
 * <AUTHOR>
 * @data 2025-03-13 16:03
 * @filePath assets\core\manager\timer\TimerManager.ts
 * @description 计时器管理器 - 负责管理游戏中的所有计时器
 * 支持倒计时、循环计时、延时执行等功能
 */
@ccclass('TimerManager')
export class TimerManager extends BaseManager {
    /** 倒计时数据 - 存储所有正在运行的计时器信息 */
    private times: Record<string, TimerData> = {};

    /**
     * 组件加载时调用
     * 初始化计时器管理器
     */
    protected onLoad(): void {
        this.log("定时器管理器初始化完成");
    }

    /**
     * 每帧更新
     * 更新所有计时器的状态
     * @param dt 帧间隔时间
     */
    protected update(dt: number): void {
        for (let key in this.times) {
            this.updateTimer(this.times[key], dt);
        }
    }

    /**
     * 更新单个计时器
     * @param data 计时器数据
     * @param dt 帧间隔时间
     */
    private updateTimer(data: TimerData, dt: number): void {
        if (data.timer.update(dt) && data.totalTime > 0) {
            if (data.totalTime == Number.MAX_VALUE && data.onSecond) {
                // 相当于不会停止的计时器
                data.onSecond.call(data.object);
                return;
            }
            data.totalTime--;
            if (typeof data.field === "string") data.object[data.field] = data.totalTime;
            if (data.totalTime == 0) {
                // 倒计时结束触发
                this.onTimerComplete(data);
            } else if (data.onSecond) {
                // 触发每秒回调事件  
                data.onSecond.call(data.object);
            }
        }
    }

    /**
     * 游戏恢复时加载时间数据
     * 处理游戏从后台恢复时的计时器状态
     */
    public load(): void {
        for (const key in this.times) {
            const interval = Math.floor((this.getTime() - (this.times[key].startTime || this.getTime())) / 1000);
            const data = this.times[key];
            data.totalTime = math.clamp(data.totalTime - interval, 0, data.totalTime);
            if (typeof data.field === "string") {
                data.object[data.field] = data.totalTime;
            }
            if (data.totalTime <= 0) {
                data.totalTime = 0;
                this.onTimerComplete(data);
            } else {
                this.times[key].startTime = null;
            }
        }
    }

    /**
     * 触发倒计时完成事件
     * @param data 计时器数据
     */
    private onTimerComplete(data: TimerData): void {
        if (data.onComplete) data.onComplete.call(data.target || data.object, data.object);
        delete this.times[data.id];
    }

    /**
     * 获取游戏开始到现在逝去的时间
     * @returns 游戏运行时间（秒）
     */
    private getTime(): number {
        return game.totalTime;
    }

    /**
     * 游戏最小化时保存时间数据
     * 记录所有计时器的当前状态
     */
    public save(): void {
        for (let key in this.times) {
            this.times[key].startTime = this.getTime();
        }
    }

    /**
     * 在指定对象上注册一个倒计时的回调管理器
     * @param object        注册定时器的对象
     * @param field         时间字段或总时间值
     * @param onSecond      每秒触发的事件回调
     * @param onComplete    倒计时完成时的事件回调
     * @param target        触发事件的目标对象
     * @param step          计时器步长（秒）
     * @returns 计时器唯一标识
     * @example
    export class Test extends Component {
        private timeId!: string;
        
        start() {
            // 在指定对象上注册一个倒计时的回调管理器
            this.timeId = app.timer.register(this, "countDown", this.onSecond, this.onComplete);
        }
        
        private onSecond() {
            console.log("每秒触发一次");
        }

        private onComplete() {
            console.log("倒计时完成触发");
        }
    }
     */
    public register(object: object, field: string | number, onSecond?: Function, onComplete?: Function, target: object | null = null, step: number = 1): string {
        const timer = new Timer(step);
        const id = StringUtil.guid();
        let totalTime = typeof field === "string" ? (object as any)[field] : field;
        this.times[id] = {
            id: id,
            timer: timer,
            object: object,
            field: field,
            onSecond: onSecond,
            onComplete: onComplete,
            target: target,
            startTime: null,
            totalTime: totalTime
        };
        return id;
    }

    /**
     * 注册一个不会停止的循环计时器
     * @param object        注册定时器的对象
     * @param onSecond      每秒触发的事件回调
     * @param step          计时器步长（秒）
     * @returns 计时器唯一标识
     */
    public registerLoop(object: object, onSecond: Function, step: number = 1): string {
        return this.register(object, Number.MAX_VALUE, onSecond, undefined, null, step);
    }

    /** 
     * 在指定对象上注销一个倒计时的回调管理器 
     * @param id         计时器唯一标识
     * @example
    export class Test extends Component {
        private timeId!: string;

        start() {
            this.timeId = oops.timer.register(this, "countDown", this.onSecond, this.onComplete);
        }

        onDestroy() {
            // 在指定对象上注销一个倒计时的回调管理器
            oops.timer.unRegister(this.timeId);
        }
    }
     */
    public unRegister(id: string): void {
        if (this.times[id]) delete this.times[id];
    }

    /**
     * 停止计时器
     * @param id 计时器唯一标识
     */
    public stop(id: string): void {
        if (this.times[id]) this.times[id].timer.stop();
    }

    /**
     * 重置计时器
     * @param id 计时器唯一标识
     * @param totalTime 新的总时间（可选）
     */
    public reset(id: string, totalTime?: number): void {
        if (this.times[id]) {
            if (totalTime) this.times[id].totalTime = totalTime;
            this.times[id].timer.reset();
        }
    }
}