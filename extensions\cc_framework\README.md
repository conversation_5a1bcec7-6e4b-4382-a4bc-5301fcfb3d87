# 项目简介

cocos项目框架插件

## 安装

```bash
# 安装依赖模块
npm install
# 构建
npm run build
```

## 扩展引用说明
要在你的项目中引用此框架，请按照以下步骤操作：

1. 在项目根目录下创建extensions文件夹（如果不存在）：
```bash
mkdir extensions
```

2. 添加框架作为git子模块：
```bash
git <NAME_EMAIL>:zpqq132555/cc_framework.git extensions/cc_framework
```

3. 初始化并更新子模块：
```bash
git submodule init
git submodule update
```

4. 在Cocos Creator中，确保在项目设置中启用了该扩展。

## 使用说明
```bash
# 安装依赖模块
npm install
# 构建
npm run build
```