/** 
 * 界面层类型枚举
 * @description 定义了游戏中不同类型的UI层级
 * 用于管理和组织不同用途的界面，确保正确的显示顺序和交互
 */
export enum LayerType {
    // /** 二维游戏层 - 用于显示游戏主场景 */
    // Game = "LayerGame",

    /** 主界面层 - 用于显示游戏的主要界面，如主菜单、游戏界面等 */
    UI = "UI_Layer",

    /** 弹窗层 - 用于显示普通的弹窗界面，支持多个弹窗同时显示 */
    Pop = "Pop_Layer",

    /** 模式窗口层 - 用于显示模态弹窗，一次只显示一个，会阻止其他界面交互 */
    Top = "Top_Layer",

    // /** 滚动消息提示层 - 用于显示游戏中的临时提示信息 */
    // Notify = "LayerNotify",

    // /** 新手引导层 - 用于显示新手引导相关的界面 */
    // Guide = "LayerGuide"
}
/** 
 * 界面配置接口
 * @description 定义了UI界面的基本配置信息
 * 用于控制界面的加载、显示和资源管理
 */
export interface UIConfig {
    /** 远程资源包名 - 指定界面资源所在的资源包，默认为本地资源包 */
    bundle?: string;

    /** 预制资源相对路径 - 指定界面预制件的加载路径 */
    prefab: string;

    /** 是否自动释放 - 控制界面关闭时是否自动释放资源，默认为false */
    destroy?: boolean;
}

/**
 * 界面打开动画类型
 */
export enum UIViewShowType {
    Normal,
    Scale,
    Opacity
}