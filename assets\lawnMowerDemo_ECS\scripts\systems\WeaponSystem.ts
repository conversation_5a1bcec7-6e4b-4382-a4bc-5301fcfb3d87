import { Vec2 } from "cc";
import { Entity } from "db://framework/ECS/Entity";
import { EntitySystem } from "db://framework/ECS/systems/EntitySystem";
import { Matcher } from "db://framework/ECS/utils/Matcher";
import { Time } from "db://framework/utils/Time";
import { Transform } from "../components/Transform";
import { Weapon } from "../components/Weapon";
import { EntityTags } from "../EntityTags";

/**
 * <AUTHOR>
 * @data 2025-08-04 10:37
 * @filePath assets\lawnMowerDemo_ECS\scripts\systems\WeaponSystem.ts
 * @description 武器系统 - 处理自动攻击和子弹生成
 */
export class WeaponSystem extends EntitySystem {

    constructor() {
        super(Matcher.empty().all(Transform, Weapon));
    }
    protected process(entities: Entity[]): void {
        const deltaTime = Time.deltaTime;

        for (const entity of entities) {
            const weapon = entity.getComponent(Weapon);
            const transform = entity.getComponent(Transform);

            if (!weapon || !transform) continue;

            weapon.updateTimer(deltaTime);

            if (weapon.autoFire && weapon.canFire()) {
                const target = this.findNearestTarget(new Vec2(transform.position.x, transform.position.y));
                if (target) {
                    const direction = target.clone().subtract(new Vec2(transform.position.x, transform.position.y)).normalize();
                    this.createProjectile(new Vec2(transform.position.x, transform.position.y), direction, weapon);
                    weapon.resetFireTimer();
                }
            }
        }
    }
    /** 创建子弹 */
    private createProjectile(position: Vec2, direction: Vec2, weapon: Weapon): void {
        const projectile = this.scene.createEntity("Bullet");
        projectile.tag = EntityTags.BULLET;

        const transform = new Transform(position.x, position.y, 0);
        transform.rotation = Math.atan2(direction.y, direction.x);
        projectile.addComponent(transform);

        const projectileComponent = new Projectile(weapon.damage, weapon.bulletSpeed, weapon.bulletLifeTime);
        projectileComponent.setDirection(direction);
        projectile.addComponent(projectileComponent);

        const renderable = RenderSystem.createBullet();
        projectile.addComponent(renderable);

        const collider = new ColliderComponent('circle');
        collider.setSize(weapon.bulletSize);
        projectile.addComponent(collider);
    }
    private findNearestTarget(position: Vec2): Vec2 | null {
        const enemyEntities = this.scene.findEntitiesByTag(EntityTags.ENEMY);
        let nearestTarget: Vec2 | null = null;
        let nearestDistance = Infinity;

        for (const entity of enemyEntities) {
            const transform = entity.getComponent(Transform);
            if (transform) {
                const targetPos = new Vec2(transform.position.x, transform.position.y);
                const distance = Vec2.distance(position, targetPos);
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestTarget = targetPos;
                }
            }
        }

        return nearestTarget;
    }

}