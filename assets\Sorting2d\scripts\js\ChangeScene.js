System.register("chunks:///_virtual/ChangeScene.ts", ["./rollupPluginModLoBabelHelpers.js", "cc"], (function (e) {
    var n, c, t, o, r;
    return {
        setters: [function (e) {
            n = e.inherits<PERSON><PERSON>e
        }
            , function (e) {
                c = e.cclegacy,
                    t = e._decorator,
                    o = e.director,
                    r = e.Component
            }
        ],
        execute: function () {
            var u;
            c._RF.push({}, "74471ApXcJC0ZfasXP4OF3f", "ChangeScene", void 0);
            var i = t.ccclass;
            t.property,
                e("ChangeScene", i("ChangeScene")(u = function (e) {
                    function c() {
                        return e.apply(this, arguments) || this
                    }
                    n(c, e);
                    var t = c.prototype;
                    return t.start = function () { }
                        ,
                        t.update = function (e) { }
                        ,
                        t.changeScene = function (e, n) {
                            console.log(n),
                                this.scheduleOnce((function () {
                                    o.loadScene(n)
                                }
                                ), .5)
                        }
                        ,
                        c
                }(r)) || u);
            c._RF.pop()
        }
    }
}
));