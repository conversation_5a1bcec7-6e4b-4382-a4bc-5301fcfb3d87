{
    // "extends": "../../temp/tsconfig.cocos.json",
    "extends":"./base.tsconfig.json",
    "compilerOptions": {
        "outDir": "./dist",
        "rootDir": "./source",
        "types": [
            "node",
            "@cocos/creator-types/editor",
            // "../../temp/declarations/cc.custom-macro",
            // "../../temp/declarations/jsb",
            // "../../temp/declarations/cc",
            // "../../temp/declarations/cc.env",
        ],
    },
    "exclude": [
        "assets/**/*",
    ]
}