import { DEV } from 'cc/env';
import { DateUtil } from '../../utils/DateUtil';

export interface ILog {
    (title: string, ...args: any[]): void
}

/**
 * 日志管理类，用于统一日志输出格式
 */
export class Debug {
    /** 创建日志输出函数 */
    public static create(level: 'log' | 'warn' | 'error', styleColor: string, title: string, titleColor = '#fff'): ILog {
        if (DEV) {
            return window.console[level].bind(window.console,
                '%c %s %c %s ',
                `background:${styleColor}; padding: 2px; border-radius: 5px 0 0 5px; border: 1px solid ${styleColor}; color: ${titleColor}; font-weight: normal;`,
                `${title} ${DateUtil.currentDate.toLocaleString()}`,
                `background:#ffffff ; padding: 2px; border-radius: 0 5px 5px 0; border: 1px solid ${styleColor}; color: ${styleColor}; font-weight: normal;`
            );
        }
        return window.console[level].bind(window.console,
            `${title} [${DateUtil.currentDate.toLocaleString()}]`
        );
    }

    /** 用于输出一般信息 */
    public static get log(): ILog {
        return Debug.create('log', '#6495ed', '[LOG]', '#000') as ILog;
    }

    /** 用于输出警告信息 */
    public static get warn(): ILog {
        return Debug.create('warn', '#ff7f50', '[WARN]', '#000') as ILog;
    }

    /** 用于输出错误信息 */
    public static get error(): ILog {
        return Debug.create('error', '#ff4757', '[ERROR]', '#000') as ILog;
    }

    /** 用于输出调试信息 */
    public static get debug(): ILog {
        return Debug.create('log', '#ff6347', '[DEBUG]', '#000') as ILog;
    }

    /** 用于输出成功信息 */
    public static get success(): ILog {
        return Debug.create('log', '#00ae9d', '[SUCC]', '#000') as ILog;
    }
}