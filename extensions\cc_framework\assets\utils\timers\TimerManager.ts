import { GlobalManager } from "../GlobalManager";

/**
 * <AUTHOR>
 * @data 2025-08-01 09:08
 * @filePath extensions\cc_framework\assets\utils\timers\TimerManager.ts
 * @description 定时器管理器,允许动作的延迟和重复执行
 */
export class TimerManager extends GlobalManager {

    // public _timers: Timer[] = [];

    // public override update() {
    //     for (let i = this._timers.length - 1; i >= 0; i--) {
    //         if (this._timers[i].tick()) {
    //             this._timers[i].unload();
    //             this._timers.splice(i, 1);
    //         }
    //     }
    // }

    // /**
    //  * 调度一个一次性或重复的计时器，该计时器将调用已传递的动作
    //  * @param timeInSeconds
    //  * @param repeats
    //  * @param context
    //  * @param onTime
    //  */
    // public schedule(timeInSeconds: number, repeats: boolean, context: any, onTime: (timer: ITimer) => void) {
    //     let timer = new Timer();
    //     timer.initialize(timeInSeconds, repeats, context, onTime);
    //     this._timers.push(timer);

    //     return timer;
    // }

}