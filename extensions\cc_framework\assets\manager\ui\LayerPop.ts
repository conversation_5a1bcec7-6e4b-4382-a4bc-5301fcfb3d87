import { BlockInputEvents, EventTouch, Node } from "cc";
import { BaseView } from "../../base/BaseView";
import { ViewUtil } from "../../utils/ViewUtil";
import { LayerUI, ViewParams } from "./LayerUI";

/** 遮罩预制件路径 */
const Mask: string = 'frameworkCommon/prefab/mask';

/**
 * <AUTHOR>
 * @data 2025-03-13 15:46
 * @filePath assets\core\manager\ui\LayerPop.ts
 * @description 弹窗层 - 负责管理游戏中的弹窗界面
 * 支持多窗口叠加、遮罩层、点击空白区域关闭等功能
 */
export class LayerPop extends LayerUI {
    /** 触摸事件阻挡组件 - 用于阻止点击穿透 */
    protected black!: BlockInputEvents;
    /** 半透明遮罩节点 - 用于显示背景遮罩 */
    protected mask: Node = null!;

    /**
     * 构造函数
     * @param name 层名称
     */
    constructor(name: string) {
        super(name);
        this.init();
    }

    /**
     * 初始化弹窗层
     * 注册子节点添加/移除事件，加载遮罩预制件
     */
    private init(): void {
        this.on(Node.EventType.CHILD_ADDED, this.onChildAdded, this);
        this.on(Node.EventType.CHILD_REMOVED, this.onChildRemoved, this);
        ViewUtil.createPrefabNodeAsync(Mask).then(res => {
            this.mask = res;
            this.mask.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);

            this.black = this.mask.addComponent(BlockInputEvents);
            this.black.enabled = false;
        });
    }

    /**
     * 子节点添加事件处理
     * @param child 添加的子节点
     * @description 调整遮罩层级，确保遮罩在窗口下方
     */
    private onChildAdded(child: Node): void {
        if (this.mask) {
            this.mask.setSiblingIndex(this.children.length - 2);
        }
    }

    /**
     * 子节点移除事件处理
     * @param child 移除的子节点
     * @description 调整遮罩层级，确保遮罩在窗口下方
     */
    private onChildRemoved(child: Node): void {
        if (this.mask) {
            this.mask.setSiblingIndex(this.children.length - 2);
        }
    }

    /**
     * 显示UI界面
     * @param vp 视图参数
     * @param params 界面参数
     * @description 显示界面并启用遮罩和点击阻挡
     */
    protected async showUi(vp: ViewParams, params: { uiId: number, uiArgs: any }): Promise<void> {
        await super.showUi(vp, params);
        if (vp.node) {
            // 界面加载完成显示时，启动触摸非窗口区域关闭
            this.openVacancyRemove(vp);

            // 界面加载完成显示时，层级事件阻挡
            this.black.enabled = true;
        }
    }

    /**
     * 启动触摸非窗口区域关闭功能
     * @param vp 视图参数
     * @description 根据界面配置决定是否显示遮罩
     */
    protected openVacancyRemove(vp: ViewParams): void {
        if (vp.node!.getComponent(BaseView)!.mask) {
            this.mask.parent = this;
        }
    }

    /**
     * 触摸非窗口区域关闭事件处理
     * @param event 触摸事件
     * @description 点击空白区域时关闭最上层窗口
     */
    private onTouchEnd(event: EventTouch): void {
        if (this.ui_nodes.size > 0) {
            let vp = this.ui_nodes.array[this.ui_nodes.size - 1];
            if (vp.valid && vp.node!.getComponent(BaseView)!.vacancy) {
                this.remove(vp.config.prefab, vp.config.destroy);
            }
        }
    }

    /**
     * 窗口关闭事件处理
     * @param vp 视图参数
     * @description 关闭窗口后处理遮罩和点击阻挡
     */
    protected onCloseWindow(vp: ViewParams): void {
        super.onCloseWindow(vp);
        // 界面关闭后，关闭触摸事件阻挡、关闭触摸非窗口区域关闭、关闭遮罩
        this.setBlackDisable();
    }

    /**
     * 设置触摸事件阻挡
     * @description 当所有弹窗关闭时，禁用点击阻挡
     */
    protected setBlackDisable(): void {
        // 所有弹窗关闭后，关闭事件阻挡功能
        if (this.ui_nodes.size == 0) {
            this.black.enabled = false;
            this.closeVacancyRemove();
            this.closeMask();
        }
    }

    /**
     * 关闭触摸非窗口区域关闭功能
     * @description 当没有需要点击空白区域关闭的窗口时，移除事件监听
     */
    protected closeVacancyRemove(): void {
        let flag = true;
        for (let value of this.ui_nodes.values()) {
            if (value.node?.getComponent(BaseView)?.vacancy) {
                flag = false;
                break;
            }
        }
        if (flag && this.hasEventListener(Node.EventType.TOUCH_END, this.onTouchEnd, this)) {
            this.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        }
    }

    /**
     * 关闭遮罩
     * @description 当没有需要显示遮罩的窗口时，移除遮罩
     */
    protected closeMask(): void {
        if (this.mask == null) return;
        let flag = true;
        for (let value of this.ui_nodes.values()) {
            if (value.node?.getComponent(BaseView)?.mask) {
                flag = false;
                break;
            }
        }
        if (flag) {
            this.mask.parent = null;
        }
    }

    /**
     * 清除所有窗口
     * @param isDestroy 是否销毁窗口
     * @description 清除所有窗口并重置遮罩和点击阻挡状态
     */
    public clear(isDestroy: boolean): void {
        super.clear(isDestroy)
        if (this.black) this.black.enabled = false;
        this.closeVacancyRemove();
        this.closeMask();
    }
}