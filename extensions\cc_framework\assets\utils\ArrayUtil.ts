/**
 * @ path: assets\core\utils\ArrayUtil.ts
 * @ author: OldPoint
 * @ data: 2025-04-02 22:49
 * @ description: 
 */
/**
 * 数组工具类
 * @description 提供数组操作相关的常用工具方法
 * 包括数组元素删除、数组合并等功能
 */
export class ArrayUtil {

    /** 
     * 删除数组中的指定元素
     * @description 从数组中删除第一个匹配的元素
     * @param array 要操作的数组
     * @param item 要删除的元素
     * @example
     * const arr = [1, 2, 3, 4, 5];
     * ArrayUtil.removeItem(arr, 3); // arr 变为 [1, 2, 4, 5]
     */
    public static removeItem<T>(array: Array<T>, item: T): void {
        const temp = array.concat();
        for (let i = 0; i < temp.length; i++) {
            const value = temp[i];
            if (item == value) {
                array.splice(i, 1);
                break;
            }
        }
    }

    /**
     * 合并两个数组
     * @description 将两个数组合并成一个新数组，不修改原数组
     * @param array1 第一个数组
     * @param array2 第二个数组
     * @returns 合并后的新数组
     * @example
     * const arr1 = [1, 2, 3];
     * const arr2 = [4, 5, 6];
     * const result = ArrayUtil.combineArrays(arr1, arr2); // 返回 [1, 2, 3, 4, 5, 6]
     */
    public static combineArrays<T>(array1: Array<T>, array2: Array<T>): Array<T> {
        return [...array1, ...array2];
    }

    /**
     * 根据权重从数组中随机选择一个索引
     * @param weightArray 权重数组
     * @returns 随机选择的索引
     */
    public static getWeightedRandomIndex(weightArray: number[]): number {
        let totalWeight = 0;
        weightArray.forEach(weight => totalWeight += weight);
        totalWeight *= Math.random();
        for (let index = 0; index < weightArray.length; index++) {
            if ((totalWeight -= weightArray[index]) <= 0) {
                return index;
            }
        }
        return 0;
    }

}