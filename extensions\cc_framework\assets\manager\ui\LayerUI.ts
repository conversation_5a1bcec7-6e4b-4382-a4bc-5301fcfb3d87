import { _decorator, AssetManager, instantiate, js, Layers, Node, Prefab, SafeArea, Widget } from 'cc';
import { BaseView } from '../../base/BaseView';
import { APP } from '../../core/APP';
import { Debug } from '../../core/logger/Debug';
import { Collection } from '../../lib/collection/Collection';
import { UIConfig } from './Defines';
const { ccclass, property } = _decorator;

/**
 * 视图参数接口 - 用于存储UI界面的配置和状态信息
 */
export interface ViewParams {
    /** 界面配置 - 包含预制件路径、资源包名等信息 */
    config: UIConfig;
    /** 是否在使用状态 - 标记界面是否正在显示 */
    valid: boolean;
    /** 界面根节点 - 界面的实际节点对象 */
    node: Node | null;
}

/**
 * <AUTHOR>
 * @data 2025-03-13 15:44
 * @filePath assets\core\manager\ui\LayerUI.ts
 * @description 界面层对象 - 负责管理UI界面的显示、隐藏和缓存
 * 提供界面的加载、显示、移除、缓存等功能
 */
export class LayerUI extends Node {
    /** 类名 - 用于日志输出 */
    private className: string = js.getClassName(this);
    /** 加载超时时间 - 单位毫秒 */
    private readonly loadingTimeoutGui: number = 1000;
    /** 显示界面节点集合 - 存储当前显示的UI界面 */
    protected ui_nodes = new Collection<string, ViewParams>();
    /** 被移除的界面缓存数据 - 存储已关闭但未销毁的UI界面 */
    protected ui_cache = new Map<string, ViewParams>();

    /**
     * UI基础层构造函数
     * @param name 该层名称
     * @description 初始化UI层，设置对齐方式和适配模式
     */
    constructor(name: string) {
        super(name);
        this.layer = Layers.Enum.UI_2D;
        const widget: Widget = this.addComponent(Widget);
        widget.isAlignLeft = widget.isAlignRight = widget.isAlignTop = widget.isAlignBottom = true;
        widget.left = widget.right = widget.top = widget.bottom = 0;
        widget.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;
    }

    /**
     * 清除所有节点
     * @param isDestroy 移除后是否释放资源
     * @description 清除所有显示的界面和缓存中的界面
     */
    public clear(isDestroy: boolean): void {
        // 清除所有显示的界面
        this.ui_nodes.forEach((value: ViewParams, key: string) => {
            this.remove(value.config.prefab, isDestroy);
            value.valid = false;
        });
        this.ui_nodes.clear();

        // 清除缓存中的界面
        if (isDestroy) {
            this.ui_cache.forEach((value: ViewParams, prefabPath: string) => {
                this.removeCache(prefabPath);
            });
        }
    }

    /**
     * 根据预制件路径删除界面
     * @param prefabPath 预制件路径
     * @param isDestroy 移除后是否释放资源
     * @description 删除指定路径的预制件界面，包括队列中的和多个实例
     */
    public remove(prefabPath: string, isDestroy?: boolean): void {
        let release: any = undefined;
        if (isDestroy !== undefined) release = isDestroy;

        // 界面移出舞台
        const vp = this.ui_nodes.get(prefabPath);
        if (vp) {
            // 优先使用参数中控制的释放条件，如果未传递参数则用配置中的释放条件，默认不缓存关闭的界面
            if (release === undefined) {
                release = vp.config.destroy !== undefined ? vp.config.destroy : false;
            }

            // 不释放界面，缓存起来待下次使用
            if (release === false) {
                this.ui_cache.set(vp.config.prefab, vp);
            }

            const comp = vp.node?.getComponent(BaseView)!;
            // @ts-ignore
            comp.removeEvent();
            if (comp.onBeforeRemove) {
                comp.onBeforeRemove(() => {
                    this.removed(vp, isDestroy);
                });
            } else {
                this.removed(vp, isDestroy);
            }
        }

        // 验证是否删除后台缓存界面
        if (release === true) this.removeCache(prefabPath);
    }

    /**
     * 窗口组件中触发移除事件与释放窗口对象
     * @param vp 视图参数
     * @param isDestroy 是否销毁
     * @description 处理窗口关闭事件和资源释放
     */
    private removed(vp: ViewParams, isDestroy?: boolean): void {
        vp.valid = false;
        // 界面移除舞台事件
        this.onCloseWindow(vp);

        if (isDestroy) {
            // 释放界面显示对象
            vp.node?.destroy();
            // 释放界面相关资源
            APP.res.release(vp.config.prefab);
        } else {
            vp.node?.removeFromParent();
        }
    }

    /**
     * 删除缓存的界面
     * @param prefabPath 预制件路径
     * @description 当缓存界面被移除舞台时，删除对应的缓存界面
     */
    private removeCache(prefabPath: string): void {
        let vp = this.ui_cache.get(prefabPath);
        if (vp) {
            this.onCloseWindow(vp);
            this.ui_cache.delete(prefabPath);
            const childNode = vp.node;
            childNode?.destroy();
        }
    }

    /**
     * 加载界面资源
     * @param prefabPath 预制件路径
     * @param bundle 远程资源包名，默认为本地资源包
     * @returns 加载的节点对象
     * @description 异步加载界面预制件资源
     */
    public async load(prefabPath: string, bundle: string = AssetManager.BuiltinBundleName.RESOURCES): Promise<Node | null> {
        // 加载界面资源超时提示
        const timerId = setTimeout(this.onLoadingTimeoutGui, this.loadingTimeoutGui);
        const res = await APP.res.loadAsync(bundle, prefabPath, Prefab);
        let node: Node | null = null;
        if (res) {
            node = instantiate(res);
            const baseView = node.getComponent(BaseView);
            if (baseView) {
                // 是否启动真机安全区域显示
                if (baseView.safeArea) node.addComponent(SafeArea);
                // @ts-ignore
                baseView.onCloseWindow = this.onCloseWindow.bind(this);
            } else {
                Debug.error(this.className, `路径为【${prefabPath}】的预制加载失败,没有找到BaseView组件`);
            }
        } else {
            Debug.error(this.className, `路径为【${prefabPath}】的预制加载失败`);
        }
        // 关闭界面资源超时提示
        APP.ui.waitClose();
        clearTimeout(timerId);
        return node;
    }

    /**
     * 窗口关闭事件处理
     * @param vp 视图参数
     * @description 从节点集合中删除关闭的窗口
     */
    protected onCloseWindow(vp: ViewParams): void {
        this.ui_nodes.delete(vp.config.prefab);
    }

    /**
     * 加载超时事件处理
     * @description 显示等待界面
     */
    private onLoadingTimeoutGui(): void {
        APP.ui.waitOpen();
    }

    /**
     * 添加预制件节点到层容器
     * @param config 界面配置数据
     * @param params 自定义参数
     * @param node 预制件节点
     * @description 添加界面到UI层，并处理重复加载的情况
     */
    public add(config: UIConfig, params: { uiId: number, uiArgs: any }, node: Node | null = null): void {
        if (this.ui_nodes.has(config.prefab)) {
            console.warn(`路径为【${config.prefab}】的预制重复加载`);
            return;
        }
        // 检查缓存中是否存界面
        let vp: ViewParams = this.ui_cache.get(config.prefab) || { config: config, valid: false, node: null };
        vp.config = config;
        vp.valid = true;
        vp.node = node;
        this.ui_nodes.set(config.prefab, vp);
        this.showUi(vp, params);
    }

    /**
     * 创建并显示界面节点
     * @param vp 视图参数
     * @param params 界面参数
     * @description 加载并显示界面，触发相应的生命周期事件
     */
    protected async showUi(vp: ViewParams, params: { uiId: number, uiArgs: any }): Promise<void> {
        // 标记界面为使用状态
        vp.valid = true;
        // 如果节点为空，则加载节点
        if (!vp.node) vp.node = await this.load(vp.config.prefab, vp.config.bundle) as Node;
        if (vp.node) {
            // 触发窗口添加事件
            this.addChild(vp.node);
            const baseView = vp.node!.getComponent(BaseView);
            if (baseView) {
                // @ts-ignore
                baseView.onAdded(params);
            }
        }
    }
}