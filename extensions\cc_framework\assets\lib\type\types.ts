/**
 * TypeScript 工具类型库：条件互斥联合封装
 * 
 * 主要功能：
 * 1. 支持基于标签区分的联合类型（discriminated union）
 * 2. 支持公共基础字段
 * 3. 支持条件字段扩展
 * 4. 支持多触发字段
 * 5. 支持嵌套联合类型
 * 
 * 使用场景：
 * - 状态管理
 * - API 响应类型定义
 * - 复杂表单类型
 * - 需要条件字段的场景
 */

// /** 
//  * XOR 类型：确保两个类型互斥（不能同时存在）
//  * 
//  * @example
//  * type A = { a: string };
//  * type B = { b: number };
//  * type C = XOR<A, B>;
//  * // C 可以是 { a: string, b?: never } 或 { a?: never, b: number }
//  */
// type XOR<T, U> = (T | U) & { [K in keyof T & keyof U]?: never };

/** 
 * 提取类型 T 中所有可选属性名的联合类型
 * 
 * @example
 * type T = { a?: string; b: number; c?: boolean };
 * type Optional = OptionalKeys<T>; // "a" | "c"
 */
type OptionalKeys<T> = { [K in keyof T]-?: {} extends Pick<T, K> ? K : never }[keyof T];

/** 
 * 判断类型 T 是否是非空字符串类型
 * 
 * @example
 * type A = IsNonEmptyString<"hello">; // true
 * type B = IsNonEmptyString<"">;      // false
 * type C = IsNonEmptyString<number>;  // false
 */
type IsNonEmptyString<T> = T extends string ? T extends "" ? false : true : false;

/**
 * 基础联合类型：根据标签字段区分不同的类型分支
 * 
 * @example
 * type NodeTypes = {
 *   button: { onClick: () => void };
 *   input: { value: string };
 * };
 * type Base = { id: string };
 * type Result = DiscriminatedUnion<"type", NodeTypes, Base>;
 * // Result 类型为：
 * // | (Base & { type: "button" } & { onClick: () => void })
 * // | (Base & { type: "input" } & { value: string })
 */
export type DiscriminatedUnion<
    Tag extends string,
    Map extends Record<string, object>,
    Base extends object = {}
> = {
    [K in keyof Map]: Base & { [P in Tag]: K } & Map[K]
}[keyof Map];

// /**
//  * 条件属性类型：根据特定字段的值添加不同的属性
//  * 
//  * @example
//  * // 使用枚举
//  * enum GuideSignType {
//  *   FRAME = "FRAME",
//  *   HAND = "HAND"
//  * }
//  * 
//  * // 定义基础类型
//  * interface BaseGuideNode {
//  *   guideText: string;
//  *   guideSignType?: GuideSignType;
//  * }
//  * 
//  * // 定义不同 guideSignType 对应的额外属性
//  * type GuideSignTypeProperties = {
//  *   [GuideSignType.FRAME]: {
//  *     frameColor: string;
//  *     frameWidth: number;
//  *   };
//  *   [GuideSignType.HAND]: {
//  *     handPosition: Vec2;
//  *     handRotation: number;
//  *   };
//  * };
//  * 
//  * // 组合类型 - Field 参数会自动从 BaseGuideNode 的键中选取
//  * type GuideNodeData = DiscriminatedUnion<"guideType", GuideNodeOptions, 
//  *   ConditionalProperties<BaseGuideNode, "guideSignType", GuideSignTypeProperties>
//  * >;
//  * 
//  * // 使用示例
//  * const frameGuide: GuideNodeData = {
//  *   guideType: GuideType.TOUCH,
//  *   path: "some/path",
//  *   guideText: "点击这里",
//  *   guideSignType: GuideSignType.FRAME,
//  *   frameColor: "#FF0000",  // 必须包含
//  *   frameWidth: 2           // 必须包含
//  * };
//  * 
//  * const handGuide: GuideNodeData = {
//  *   guideType: GuideType.TOUCH,
//  *   path: "some/path",
//  *   guideText: "点击这里",
//  *   guideSignType: GuideSignType.HAND,
//  *   handPosition: new Vec2(100, 100),  // 必须包含
//  *   handRotation: 45                   // 必须包含
//  * };
//  * 
//  * // 不设置 guideSignType 也是允许的
//  * const simpleGuide: GuideNodeData = {
//  *   guideType: GuideType.TOUCH,
//  *   path: "some/path",
//  *   guideText: "点击这里"
//  * };
//  */
// export type ConditionalProperties<
//     Base extends object,
//     Field extends keyof Base,
//     ValueMap extends Record<Base[Field] & (string | number | symbol), object>
// > = Base[Field] extends keyof ValueMap
//     ? Base & Required<ValueMap[Base[Field]]>
//     : Base[Field] extends undefined
//         ? Base
//         : Base;

// /**
//  * 组合类型：将基础类型和条件属性组合在一起
//  * 
//  * @example
//  * type Result = CombineTypes<BaseType, ConditionalType>;
//  */
// export type CombineTypes<Base, Conditional> = Base & Conditional;

// /**
//  * 带条件属性的联合类型：结合 DiscriminatedUnion 和 ConditionalProperties
//  * 
//  * @example
//  * type Result = DiscriminatedUnionWithConditional<
//  *   "type",
//  *   NodeTypes,
//  *   BaseType,
//  *   "field",
//  *   ValueMap
//  * >;
//  */
// export type DiscriminatedUnionWithConditional<
//     Tag extends string,
//     Map extends Record<string, object>,
//     Base extends object,
//     Field extends keyof Base,
//     ValueMap extends Record<Base[Field] & (string | number | symbol), object>
// > = {
//     [K in keyof Map]: Base[Field] extends keyof ValueMap
//         ? Base & { [P in Tag]: K } & Map[K] & Required<ValueMap[Base[Field]]>
//         : Base & { [P in Tag]: K } & Map[K]
// }[keyof Map];

/**
 * 多触发字段联合类型：支持多个条件字段触发额外属性
 * 
 * @example
 * type Base = { type: string; name?: string; role?: string };
 * type Map = { user: { id: string } };
 * type Extra = { permissions: string[] };
 * type Result = DiscriminatedUnionWithMultipleTriggers<
 *   "type",
 *   Map,
 *   Base,
 *   Extra,
 *   ["name", "role"]
 * >;
 * // 当 name 或 role 是非空字符串时，会附加 permissions 字段
 */
export type DiscriminatedUnionWithMultipleTriggers<
    Tag extends string,
    Map extends Record<string, object>,
    Base extends object,
    ExtraField extends object,
    TriggerKeys extends OptionalKeys<Base>[]
> = {
    [K in keyof Map]: Base & { [P in Tag]: K } & Map[K] & (
        TriggerKeys[number] extends infer T
            ? T extends OptionalKeys<Base>
                ? IsNonEmptyString<Base[T]> extends true
                    ? ExtraField
                    : {}
                : {}
            : {}
    )
}[keyof Map];

// /**
//  * 嵌套联合类型：支持多层级的类型定义
//  * 
//  * @example
//  * type NestedMap = {
//  *   user: {
//  *     basic: { id: string };
//  *     premium: { id: string; features: string[] };
//  *   };
//  *   admin: {
//  *     super: { id: string; level: number };
//  *   };
//  * };
//  * type Result = NestedDiscriminatedUnion<"type", NestedMap, { name: string }>;
//  * // 支持无限层级的嵌套类型定义
//  */
// export type NestedDiscriminatedUnion<
//     Tag extends string,
//     Map extends Record<string, object>,
//     Base extends object = {}
// > = {
//     [K in keyof Map]: Base & { [P in Tag]: K } & (
//         Map[K] extends Record<string, object>
//             ? { [Q in keyof Map[K]]: Map[K][Q] extends Record<string, object> ? NestedDiscriminatedUnion<Tag, Map[K][Q], Base> : Map[K][Q] }
//             : Map[K]
//     )
// }[keyof Map];

/** 
 * 非空字符串类型：用于类型安全的字符串验证
 * 
 * @example
 * function validateString(str: NonEmptyString) {
 *   // 确保字符串不为空
 * }
 */
export type NonEmptyString = string & { __nonEmptyBrand: never };