import { __private, Asset, assetManager, AssetManager, ImageAsset, js, resources, SpriteFrame, Texture2D } from "cc";
import { StringUtil } from "../../utils/StringUtil";
import { Debug } from "../logger/Debug";

/** 资源路径类型，可以是单个字符串或字符串数组 */
export type Paths = string | string[];
/** 资源类型，可以是 Asset 的子类构造函数或 null */
export type AssetType<T = Asset> = __private.__types_globals__Constructor<T> | null;
/** 加载进度回调函数类型 */
export type ProgressCallback = ((finished: number, total: number, item: AssetManager.RequestItem) => void) | null;
/** 加载完成回调函数类型 */
export type CompleteCallback = any;
/** 远程资源加载选项类型 */
export type IRemoteOptions = { [k: string]: any; ext?: string; } | null;

/** 资源加载参数类型 */
interface ILoadResArgs<T extends Asset> {
    /** 资源包名 */
    bundle?: string;
    /** 资源文件夹名 */
    dir?: string;
    /** 资源路径 */
    paths: Paths;
    /** 资源类型 */
    type: AssetType<T>;
    /** 资源加载进度回调 */
    onProgress: ProgressCallback;
    /** 资源加载完成回调 */
    onComplete: CompleteCallback;
    /** 是否为预加载 */
    preload?: boolean;
}

/**
 * 资源加载器
 * 提供资源加载、预加载、释放等功能
 * 支持本地资源和远程资源的加载
 * 
 * <AUTHOR>
 * @date 2025-03-14 13:51
 * @filePath extensions\cc_framework\assets\core\loader\ResLoader.ts
 */
export class ResLoader {
    /** 类名，用于日志输出 */
    private className = js.getClassName(this);

    private _defaultBundleName: string = AssetManager.BuiltinBundleName.RESOURCES;
    /** 默认资源包名 */
    public get defaultBundleName(): string { return this._defaultBundleName; }
    public set defaultBundleName(value: string) { this._defaultBundleName = value; }
    /** 是否使用远程 CDN 资源 */
    private cdn: boolean = false;
    /** 资源包配置，键为包名，值为版本号 */
    private bundles: Map<string, string> = new Map<string, string>();

    //#region 加载资源
    /**
     * 加载一个资源
     * 支持多种参数组合，可以指定资源包名、路径、类型和回调函数
     * 
     * @param bundleName 资源包名
     * @param paths 资源路径
     * @param type 资源类型
     * @param onProgress 加载进度回调
     * @param onComplete 加载完成回调
     */
    public load<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public load<T extends Asset>(bundleName: string, paths: Paths, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public load<T extends Asset>(bundleName: string, paths: Paths, onComplete?: CompleteCallback): void;
    public load<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>, onComplete?: CompleteCallback): void;
    public load<T extends Asset>(paths: Paths, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public load<T extends Asset>(paths: Paths, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public load<T extends Asset>(paths: Paths, onComplete?: CompleteCallback): void;
    public load<T extends Asset>(paths: Paths, type: AssetType<T>, onComplete?: CompleteCallback): void;
    public load<T extends Asset>(
        bundleName: string,
        paths?: Paths | AssetType<T> | ProgressCallback | CompleteCallback,
        type?: AssetType<T> | ProgressCallback | CompleteCallback,
        onProgress?: ProgressCallback | CompleteCallback,
        onComplete?: CompleteCallback,
    ): void {
        let args: ILoadResArgs<T> | null = null;
        if (typeof paths === "string" || paths instanceof Array) {
            args = this.parseLoadResArgs(paths, type, onProgress, onComplete);
            args.bundle = bundleName;
        } else {
            args = this.parseLoadResArgs(bundleName, paths, type, onProgress);
            args.bundle = this._defaultBundleName;
        }
        this.loadByArgs(args);
    }
    /**
     * 根据参数加载资源
     * 内部方法，处理资源包加载和资源加载逻辑
     * 
     * @param args 资源加载参数
     */
    private async loadByArgs<T extends Asset>(args: ILoadResArgs<T>): Promise<void> {
        if (args.bundle) {
            let bundle = assetManager.bundles.get(args.bundle);
            // 获取缓存中的资源包
            if (bundle) {
                this.loadByBundleAndArgs(bundle, args);
            } else {
                // 自动加载资源包
                const v = this.cdn ? this.bundles.get(args.bundle) : "";
                bundle = await this.loadBundle(args.bundle, v);
                if (bundle) this.loadByBundleAndArgs(bundle, args);
            }
        } else {
            // 默认资源包
            this.loadByBundleAndArgs(resources, args);
        }
    }
    /**
     * 加载资源包
     * 加载指定 URL 的资源包，支持版本控制
     * 
     * @param url 资源包地址
     * @param v 资源包版本号（MD5）
     * @returns Promise<AssetManager.Bundle> 加载的资源包
     * @example
     * var serverUrl = "http://192.168.1.8:8080/";         // 服务器地址
     * var md5 = "8e5c0";                                  // Cocos Creator 构建后的MD5字符
     * await app.res.loadBundle(serverUrl, md5);
     */
    public loadBundle(url: string, v?: string): Promise<AssetManager.Bundle> {
        return new Promise<AssetManager.Bundle>((resolve, reject) => {
            assetManager.loadBundle(url, { version: v }, (err, bundle: AssetManager.Bundle) => {
                if (err) {
                    Debug.warn(this.className, err.message);
                }
                resolve(bundle);
            });
        });
    }
    /**
     * 通过资源包和参数加载资源
     * 内部方法，根据参数调用资源包的加载方法
     * 
     * @param bundle 资源包
     * @param args 资源加载参数
     */
    private loadByBundleAndArgs<T extends Asset>(bundle: AssetManager.Bundle, args: ILoadResArgs<T>): void {
        if (args.dir) {
            if (args.preload) {
                bundle.preloadDir(args.paths as string, args.type, args.onProgress, args.onComplete);
            } else {
                bundle.loadDir(args.paths as string, args.type, args.onProgress, args.onComplete);
            }
        } else {
            if (args.preload) {
                bundle.preload(args.paths as any, args.type, args.onProgress, args.onComplete);
            } else {
                bundle.load(args.paths as any, args.type, args.onProgress, args.onComplete);
            }
        }
    }
    /**
     * 解析资源加载参数
     * 内部方法，处理不同参数组合的情况
     * 
     * @param paths 资源路径
     * @param type 资源类型
     * @param onProgress 加载进度回调
     * @param onComplete 加载完成回调
     * @returns 解析后的参数对象
     */
    private parseLoadResArgs<T extends Asset>(
        paths: Paths,
        type?: AssetType<T> | ProgressCallback | CompleteCallback,
        onProgress?: AssetType<T> | ProgressCallback | CompleteCallback,
        onComplete?: ProgressCallback | CompleteCallback
    ): ILoadResArgs<T> {
        let pathsOut: any = paths;
        let typeOut: any = type;
        let onProgressOut: any = onProgress;
        let onCompleteOut: any = onComplete;
        if (onComplete === undefined) {
            const isValidType = js.isChildClassOf(type as AssetType, Asset);
            if (onProgress) {
                onCompleteOut = onProgress as CompleteCallback;
                if (isValidType) {
                    onProgressOut = null;
                }
            } else if (onProgress === undefined && !isValidType) {
                onCompleteOut = type as CompleteCallback;
                onProgressOut = null;
                typeOut = null;
            }
            if (onProgress !== undefined && !isValidType) {
                onProgressOut = type as ProgressCallback;
                typeOut = null;
            }
        }
        if (typeOut) {
            if (js.isChildClassOf(typeOut, SpriteFrame) && typeof pathsOut === "string" && pathsOut.slice(-12) !== '/spriteFrame') {
                pathsOut = pathsOut + '/spriteFrame';
            } else if (js.isChildClassOf(typeOut, Texture2D) && typeof pathsOut === "string" && pathsOut.slice(-12) !== '/texture') {
                pathsOut = pathsOut + '/texture';
            }
        }
        return { paths: pathsOut, type: typeOut, onProgress: onProgressOut, onComplete: onCompleteOut };
    }
    /**
     * 异步加载一个资源
     * 返回 Promise 对象，可以通过 await 或 then 获取加载结果
     * 
     * @param bundleName 资源包名
     * @param paths 资源路径
     * @param type 资源类型
     * @returns Promise<T> 加载的资源对象
     */
    public loadAsync<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>): Promise<T>;
    public loadAsync<T extends Asset>(bundleName: string, paths: Paths): Promise<T>;
    public loadAsync<T extends Asset>(paths: Paths, type: AssetType<T>): Promise<T>;
    public loadAsync<T extends Asset>(paths: Paths): Promise<T>;
    public loadAsync<T extends Asset>(bundleName: string,
        paths?: Paths | AssetType<T> | ProgressCallback | CompleteCallback,
        type?: AssetType<T> | ProgressCallback | CompleteCallback): Promise<T> {
        return new Promise((resolve, reject) => {
            this.load(bundleName, paths, type, (err: Error | null, asset: T) => {
                if (err) {
                    Debug.warn(this.className, err.message);
                }
                resolve(asset);
            });
        });
    }
    /**
     * 预加载文件夹中的资源
     * 预加载不会立即使用资源，只是将资源加载到内存中，以便后续快速使用
     * 
     * @param bundleName 资源包名
     * @param dir 文件夹路径
     * @param type 资源类型
     * @param onProgress 加载进度回调
     * @param onComplete 加载完成回调
     */
    public preloadDir<T extends Asset>(bundleName: string, dir: string, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public preloadDir<T extends Asset>(bundleName: string, dir: string, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public preloadDir<T extends Asset>(bundleName: string, dir: string, onComplete?: CompleteCallback): void;
    public preloadDir<T extends Asset>(bundleName: string, dir: string, type: AssetType<T>, onComplete?: CompleteCallback): void;
    public preloadDir<T extends Asset>(dir: string, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public preloadDir<T extends Asset>(dir: string, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public preloadDir<T extends Asset>(dir: string, onComplete?: CompleteCallback): void;
    public preloadDir<T extends Asset>(dir: string, type: AssetType<T>, onComplete?: CompleteCallback): void;
    public preloadDir<T extends Asset>(
        bundleName: string,
        dir?: string | AssetType<T> | ProgressCallback | CompleteCallback,
        type?: AssetType<T> | ProgressCallback | CompleteCallback,
        onProgress?: ProgressCallback | CompleteCallback,
        onComplete?: CompleteCallback,
    ): void {
        let args: ILoadResArgs<T> | null = null;
        if (typeof dir === "string") {
            args = this.parseLoadResArgs(dir, type, onProgress, onComplete);
            args.bundle = bundleName;
        } else {
            args = this.parseLoadResArgs(bundleName, dir, type, onProgress);
            args.bundle = this._defaultBundleName;
        }
        args.dir = args.paths as string;
        args.preload = true;
        this.loadByArgs(args);
    }
    /**
     * 加载文件夹中的资源
     * 加载指定文件夹中的所有资源
     * 
     * @param bundleName 资源包名
     * @param dir 文件夹路径
     * @param type 资源类型
     * @param onProgress 加载进度回调
     * @param onComplete 加载完成回调
     * @example
     * // 加载进度事件
     * var onProgressCallback = (finished: number, total: number, item: any) => {
     *     console.log("资源加载进度", finished, total);
     * }
     * 
     * // 加载完成事件
     * var onCompleteCallback = () => {
     *     console.log("资源加载完成");
     * }
     * app.res.loadDir("game", onProgressCallback, onCompleteCallback);
     */
    public loadDir<T extends Asset>(bundleName: string, dir: string, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public loadDir<T extends Asset>(bundleName: string, dir: string, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public loadDir<T extends Asset>(bundleName: string, dir: string, onComplete?: CompleteCallback): void;
    public loadDir<T extends Asset>(bundleName: string, dir: string, type: AssetType<T>, onComplete?: CompleteCallback): void;
    public loadDir<T extends Asset>(dir: string, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public loadDir<T extends Asset>(dir: string, onProgress: ProgressCallback, onComplete: CompleteCallback): void;
    public loadDir<T extends Asset>(dir: string, onComplete?: CompleteCallback): void;
    public loadDir<T extends Asset>(dir: string, type: AssetType<T>, onComplete?: CompleteCallback): void;
    public loadDir<T extends Asset>(
        bundleName: string,
        dir?: string | AssetType<T> | ProgressCallback | CompleteCallback,
        type?: AssetType<T> | ProgressCallback | CompleteCallback,
        onProgress?: ProgressCallback | CompleteCallback,
        onComplete?: CompleteCallback,
    ): void {
        let args: ILoadResArgs<T> | null = null;
        if (typeof dir === "string") {
            args = this.parseLoadResArgs(dir, type, onProgress, onComplete);
            args.bundle = bundleName;
        } else {
            args = this.parseLoadResArgs(bundleName, dir, type, onProgress);
            args.bundle = this._defaultBundleName;
        }
        args.dir = args.paths as string;
        this.loadByArgs(args);
    }
    //#endregion
    //#region 资源释放
    /**
     * 释放资源
     * 通过资源相对路径释放指定资源包中的资源
     * 
     * @param path 资源路径
     * @param bundleName 资源包名，默认为 RESOURCES
     */
    public release(path: string, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): void {
        const bundle = assetManager.getBundle(bundleName);
        if (bundle) {
            const asset = bundle.get(path);
            if (asset) {
                this.releasePrefabDepsRecursively(asset);
            }
        }
    }
    /**
     * 递归释放预制体依赖资源
     * 内部方法，用于释放资源及其依赖
     * 
     * @param uuid 资源 UUID 或资源对象
     */
    private releasePrefabDepsRecursively(uuid: string | Asset): void {
        if (uuid instanceof Asset) {
            uuid.decRef();
            // assetManager.releaseAsset(uuid);
        } else {
            const asset = assetManager.assets.get(uuid);
            if (asset) {
                asset.decRef();
                // assetManager.releaseAsset(asset);
            }
        }
    }
    //#endregion
    //#region 加载远程资源
    /**
     * 加载远程资源
     * 从远程服务器加载资源
     * 
     * @param url 资源地址
     * @param options 资源参数，例：{ ext: ".png" }
     * @param onComplete 加载完成回调
     * @example
     * var opt: IRemoteOptions = { ext: ".png" };
     * var onComplete = (err: Error | null, data: ImageAsset) => {
     *     const texture = new Texture2D();
     *     texture.image = data;
     *     
     *     const spriteFrame = new SpriteFrame();
     *     spriteFrame.texture = texture;
     *     
     *     var sprite = this.sprite.addComponent(Sprite);
     *     sprite.spriteFrame = spriteFrame;
     * }
     * app.res.loadRemote<ImageAsset>(this.url, opt, onComplete);
     */
    public loadRemote<T extends Asset>(url: string, options: IRemoteOptions | null, onComplete?: CompleteCallback): void;
    public loadRemote<T extends Asset>(url: string, onComplete?: CompleteCallback): void;
    public loadRemote<T extends Asset>(url: string, ...args: any): void {
        let options: IRemoteOptions | null = null;
        let onComplete: CompleteCallback = null;
        if (args.length == 2) {
            options = args[0];
            onComplete = args[1];
        } else {
            onComplete = args[0];
        }
        assetManager.loadRemote<T>(url, options, onComplete);
    }
    /**
     * 加载远程图片资源
     * 从远程服务器加载图片并转换为精灵帧
     * 
     * @param url 图片地址
     * @param onComplete 加载完成回调，返回精灵帧或错误
     */
    public loadRemoteImageAsset(url: string, options: ".png" | ".jpg", onComplete: (err: Error | null, img: SpriteFrame) => void): void {
        if (StringUtil.isEmpty(url)) {
            Debug.error(this.className, "url不能为空");
            onComplete(new Error("url不能为空"), null as any);
            return;
        }
        if (!url.startsWith("http")) {
            Debug.error(this.className, "url必须以http开头");
            onComplete(new Error("url必须以http开头"), null as any);
            return;
        }
        let ext: Record<string, string> = null!;
        if (!StringUtil.isEmpty(options) && !url.endsWith(options)) {
            ext = { ext: options };
        }
        this.loadRemote<ImageAsset>(url, ext, (err: Error | null, img: any) => {
            if (err) {
                Debug.error(this.className, err);
                onComplete(err, null as any);
            } else {
                const spriteFrame = new SpriteFrame();
                const texture = new Texture2D();
                texture.image = img;
                spriteFrame.texture = texture;
                onComplete(null, spriteFrame);
            }
        });
    }
    /**
     * 异步加载远程图片资源
     * 返回 Promise 对象，可以通过 await 或 then 获取加载结果
     * 
     * @param url 图片地址
     * @returns Promise<SpriteFrame> 加载的精灵帧
     */
    public loadRemoteImageAssetAsync(url: string, options: ".png" | ".jpg" = ".png"): Promise<SpriteFrame> {
        return new Promise<SpriteFrame>((resolve, reject) => {
            this.loadRemoteImageAsset(url, options, (err: Error | null, img: SpriteFrame | ImageAsset) => {
                if (err) {
                    reject(err);
                } else {
                    return resolve(img as SpriteFrame);
                }
            });
        });
    }
    //#endregion
    /**
     * 获取资源
     * 从指定资源包中获取资源
     * 
     * @param path 资源路径
     * @param type 资源类型
     * @param bundleName 资源包名，默认为 RESOURCES
     * @returns T | null 资源对象或 null
     */
    public get<T extends Asset>(path: string, type?: AssetType<T>, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): T | null {
        if (type) {
            if (js.isChildClassOf(type, SpriteFrame) && path.slice(-12) !== '/spriteFrame') {
                path = path + '/spriteFrame';
            } else if (js.isChildClassOf(type, Texture2D) && path.slice(-12) !== '/texture') {
                path = path + '/texture';
            }
        }
        const bundle: AssetManager.Bundle = assetManager.getBundle(bundleName)!;
        return bundle.get(path, type);
    }
}