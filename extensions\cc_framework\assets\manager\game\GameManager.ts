import { Camera, game, Node, screen, Size } from "cc";

/**
 * <AUTHOR>
 * @data 2025-03-15 18:02
 * @filePath assets\core\manager\game\GameManager.ts
 * @description 游戏管理器 - 负责管理游戏核心功能和状态
 * 提供游戏场景、相机、时间缩放等基础功能的控制
 * 时间缩放如果有问题那么使用 https://github.com/cocos/cocos-awesome-tech-solutions/blob/3.8.x-release/demo/Creator3.7.3_GameTimeScale/assets/scripts/GameControl.ts 这个demo
 */
export class GameManager {
    /** 游戏根节点引用 */
    private _root: Node = null!;

    /** 
     * 获取游戏根节点
     * @returns 游戏场景的根节点
     */
    public get root(): Node {
        return this._root;
    }

    /** 游戏相机引用 */
    private _camera: Camera = null!;

    /** 
     * 获取游戏相机
     * @returns 游戏场景的主相机
     */
    public get camera(): Camera {
        return this._camera;
    }

    private _timeScale: number = 1;
    /** 游戏时间缩放 */
    public get timeScale(): number {
        return this._timeScale;
    }
    public set timeScale(value: number) {
        this._timeScale = value;
    }
    private _calculateDT: any;

    /** 
     * 游戏帧率
     * @returns 帧率
     */
    public get frameRate(): number | string {
        return game.frameRate;
    }
    public set frameRate(value: number | string) {
        game.frameRate = value;
    }

    /** 
     * 获取窗口大小
     * @returns 窗口大小
     */
    public get windowSize(): Size {
        return screen.windowSize;
    }

    /**
     * 构造函数
     * @param game 游戏根节点
     * @param camera 游戏相机
     */
    constructor(game: Node, camera: Camera) {
        this._root = game;
        this._camera = camera;
        //@ts-ignore
        this._calculateDT = game._calculateDT;
        //@ts-ignore
        game._calculateDT = this.calculateDT.bind(this);
    }

    private calculateDT(useFixedDeltaTime: boolean): number {
        return this._calculateDT(useFixedDeltaTime) * this._timeScale;
    }

    // /**
    //  * 设置游戏动画速度
    //  * @param scale 时间缩放比例，1.0为正常速度
    //  * @example
    //  * // 设置游戏速度为正常速度的2倍
    //  * gameManager.setTimeScale(2.0);
    //  */
    // public setTimeScale(scale: number): void {
    //     //@ts-ignore
    //     director.globalGameTimeScale = scale;
    // }

    // /**
    //  * 获取当前游戏动画速度
    //  * @returns 当前时间缩放比例
    //  * @example
    //  * // 获取当前游戏速度
    //  * const currentSpeed = gameManager.getTimeScale();
    //  */
    // public getTimeScale(): number {
    //     //@ts-ignore
    //     return director.globalGameTimeScale;
    // }
}