import { Camera, Node, Vec3 } from 'cc';
import { signSqrtDistToPlane } from './utils';

/**
 * 2D排序模式枚举
 */
export enum EnumSort2DMode {
    Default = 0,
    Camera = 1,
    CustomAxis = 2
}

/**
 * 排序设置接口
 */
interface ISortSettings {
    dirty: boolean;
    planeNormal?: Vec3;
}

/**
 * 排序上下文接口
 */
interface ISortContext {
    camera?: Camera;
    settings?: ISortSettings;
    center?: Vec3;
}

/**
 * 排序项接口
 */
interface ISortItem {
    node: Node;
    sorting2D: {
        sortingOrder: number;
    };
    sortValue?: number;
    hasChangedFlags?: boolean;
}

/**
 * 排序器接口
 */
interface ISorter {
    ready(item: ISortItem, context: ISortContext): void;
    compare(a: ISortItem, b: ISortItem): number;
}

/**
 * 默认排序器
 */
class DefaultSort implements ISorter {
    ready(item: ISortItem): void {}
    
    compare(a: ISortItem, b: ISortItem): number {
        return a.sorting2D.sortingOrder - b.sorting2D.sortingOrder;
    }
}

/**
 * 相机排序器
 */
class CameraSort implements ISorter {
    ready(item: ISortItem, context: ISortContext): void {
        const camera = context.camera;
        if (camera && (item.node.hasChangedFlags || camera.node.hasChangedFlags || context.settings?.dirty)) {
            if (camera.projection === Camera.ProjectionType.ORTHO) {
                item.sortValue = camera.node.worldPosition.z - item.node.worldPosition.z;
            } else {
                item.sortValue = signSqrtDistToPlane(
                    camera.node.forward,
                    camera.node.worldPosition,
                    item.node.worldPosition
                );
            }
            item.sortValue /= 100;
        }
    }

    compare(a: ISortItem, b: ISortItem): number {
        return (a.sortValue ?? 0) - (b.sortValue ?? 0);
    }
}

/**
 * 自定义轴排序器
 */
class CustomAxisSort implements ISorter {
    ready(item: ISortItem, context: ISortContext): void {
        if (context.settings && (item.node.hasChangedFlags || context.settings.dirty)) {
            const settings = context.settings;
            item.sortValue = signSqrtDistToPlane(
                settings.planeNormal!,
                context.center!,
                item.node.worldPosition
            );
            item.sortValue /= 100;
        }
    }

    compare(a: ISortItem, b: ISortItem): number {
        return (a.sortValue ?? 0) - (b.sortValue ?? 0);
    }
}

/**
 * 排序器工厂
 */
export class SortFactory {
    private static _sorts: ISorter[] = [];
    private static _sortMap: Map<EnumSort2DMode, ISorter> = new Map();

    /**
     * 获取排序器
     */
    static getSort(mode: EnumSort2DMode): ISorter | undefined {
        return this._sortMap.get(mode);
    }

    /**
     * 注册排序器
     */
    static registSort(mode: EnumSort2DMode, sorter: ISorter): void {
        this._sorts.push(sorter);
        this._sortMap.set(mode, sorter);
    }

    /**
     * 添加自定义排序器
     */
    static addCustomSort(name: string, mode: EnumSort2DMode, sorter: ISorter): void {
        Object.defineProperty(EnumSort2DMode, name, {
            value: mode,
            writable: false,
            enumerable: true,
            configurable: false
        });
        this.registSort(mode, sorter);
    }
}

// 注册默认排序器
SortFactory.registSort(EnumSort2DMode.Default, new DefaultSort());
SortFactory.registSort(EnumSort2DMode.Camera, new CameraSort());
SortFactory.registSort(EnumSort2DMode.CustomAxis, new CustomAxisSort()); 