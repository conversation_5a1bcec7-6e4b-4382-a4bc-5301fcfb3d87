import { _decorator, BlockInputEvents, Color, Component, EventKeyboard, find, input, Input, Label, Mask, Node, resources, Sprite, SpriteFrame, Texture2D, UITransform, Vec3 } from 'cc';
import { DiscriminatedUnion } from 'db://framework/lib/type/types';
import { StringUtil } from 'db://framework/utils/StringUtil';

const { ccclass, property } = _decorator;

/** 引导类型枚举 */
export enum GuideType {
    /** 文本提示引导 */
    ONLY_TEXT = "onlyText",
    /** 触摸引导 */
    TOUCH = "touch",
    /** 按键引导 */
    KEY = "key"
}

/** 引导标志类型枚举 */
export enum GuideSignType {
    /** 框架类型 */
    FRAME = "frame",
    /** 手势类型 */
    HAND = "hand"
}

/** 文本位置类型 */
export type TextPosition = "follow" | "center" | "bottom";

/** 引导节点和文本数据接口 */
type GuideNodeOptions = {
    [GuideType.TOUCH]: { path: string; };
    [GuideType.KEY]: { key: string; };
    [GuideType.ONLY_TEXT]: {};
}
// 公共字段
interface BaseGuideNode {
    guideText: string;
    guideSignType?: GuideSignType;
}

// 定义不同 guideSignType 对应的额外属性
type GuideSignTypeProperties = {
    [GuideSignType.HAND]: {
        handRotation: number;
        // path: string;
    };
    [GuideSignType.FRAME]: {
        test:string
    };
};

// --- 新的 GuideNodeData 类型定义 ---
// 1. 有 guideSignType 的分支（分发式联合类型）
type GuideNodeWithSignType =
  {
    [K in keyof GuideSignTypeProperties]:
      BaseGuideNode & { guideSignType: K } & Required<GuideSignTypeProperties[K]>
  }[keyof GuideSignTypeProperties];
// 2. 没有 guideSignType 的分支
 type GuideNodeWithoutSignType = BaseGuideNode & { guideSignType?: undefined };
// 3. 合并所有情况
type GuideNodeData =
  | (DiscriminatedUnion<"guideType", GuideNodeOptions, GuideNodeWithSignType>)
  | (DiscriminatedUnion<"guideType", GuideNodeOptions, GuideNodeWithoutSignType>);

/**
 * 引导数据接口
 */
export interface GuideData {
    /** 节点和文本数据数组 */
    nodesAndTexts: GuideNodeData[];
    /** 引导标志类型 */
    guideSignType: GuideSignType;
}

/**
 * 用户引导组件
 * 提供完整的新手引导功能，支持触摸引导和按键引导
 */
@ccclass('UserGuide')
export class UserGuide extends Component {

    private _canvasNode: Node = null!;
    public set canvasNode(value: Node) {
        this._canvasNode = value;
    }

    private currentStepTemp: number = 1;
    private textPosTemp: TextPosition = "follow";
    private keyDownTimeGap: number = 1500;

    // 引导数据
    private guideData: GuideData | null = null;

    // UI组件引用
    private canvasUITransform: UITransform = new UITransform();
    private maskNode: Node | null = null;
    private blockBgNode: Node | null = null;
    private guideSignSpriteNode: Node | null = null;
    private guideTextBgSpriteNode: Node | null = null;
    private guideTextLabelNode: Node | null = null;

    protected onLoad(): void {
        this.canvasUITransform = this.node.getComponent(UITransform)!;
        // 创建所有必要的节点
        this.createMaskNode();
        this.createBlockBgNode();
        this.createGuideSignSpriteNode();
        this.createGuideTextBgSpriteNode();
        this.createGuideTextLabelNode();
    }
    //#region 更新所有必要的节点
    /** 创建引导文本标签节点 */
    private createGuideTextLabelNode(): void {
        this.guideTextLabelNode = new Node("Guide Text Label Node");
        this.guideTextLabelNode.layer = 1 << 25;

        const label = this.guideTextLabelNode.addComponent(Label);
        label.horizontalAlign = Label.HorizontalAlign.LEFT;
        label.verticalAlign = Label.VerticalAlign.TOP;
        label.overflow = Label.Overflow.SHRINK;
        label.color = new Color(0, 0, 0);
        label.fontSize = 28;

        this.guideTextBgSpriteNode!.addChild(this.guideTextLabelNode);
        this.guideTextLabelNode.setPosition(0, -5, 0);
    }

    /** 创建引导文本背景精灵节点 */
    private createGuideTextBgSpriteNode(): void {
        this.guideTextBgSpriteNode = new Node("Text Bg Sprite Node");
        this.guideTextBgSpriteNode.layer = 1 << 25;
        this.guideTextBgSpriteNode.active = false;

        const sprite = this.guideTextBgSpriteNode.addComponent(Sprite);
        sprite.type = Sprite.Type.SLICED;

        // 加载精灵帧
        resources.load("guide/defaultTextBg/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                if (this.textPosTemp === "bottom") {
                    this.setGuideTextBgSpriteSizeByCanvasSize();
                } else {
                    this.setGuideTextBgSpriteSizeByTextLength();
                }
            } else {
                console.error("加载引导文本背景节点精灵帧失败")
            }
        });

        // 加载纹理
        resources.load("guide/defaultTextBg/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
                if (this.textPosTemp === "bottom") {
                    this.setGuideTextBgSpriteSizeByCanvasSize();
                } else {
                    this.setGuideTextBgSpriteSizeByTextLength();
                }
            } else {
                console.error("加载引导文本背景节点纹理失败")
            }
        });

        this.node.addChild(this.guideTextBgSpriteNode);
    }

    /** 创建引导标志精灵节点 */
    private createGuideSignSpriteNode(): void {
        this.guideSignSpriteNode = new Node("Guide Sign Sprite Node");
        this.guideSignSpriteNode.layer = 1 << 25;
        this.guideSignSpriteNode.active = false;

        const sprite = this.guideSignSpriteNode.addComponent(Sprite);

        // 加载精灵帧
        resources.load("guide/defaultGuideSign/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载引导标志节点精灵帧失败")
            }
        });

        // 加载纹理
        resources.load("guide/defaultGuideSign/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载引导标志节点纹理失败")
            }
        });

        this.node.addChild(this.guideSignSpriteNode);
    }

    /** 创建背景阻挡节点 */
    private createBlockBgNode(): void {
        this.blockBgNode = new Node("Block Bg Node");
        this.blockBgNode.layer = 1 << 25;
        this.blockBgNode.addComponent(BlockInputEvents);

        const sprite = this.blockBgNode.addComponent(Sprite);
        sprite.color = new Color(0, 0, 0, 100);

        // 加载精灵帧
        resources.load("guide/defaultBlockBg/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                this.updateBlockBgSize();
            } else {
                console.error("加载背景阻挡节点精灵帧失败");
            }
        });

        // 加载纹理
        resources.load("guide/defaultBlockBg/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
                this.updateBlockBgSize();
            } else {
                console.error("加载背景阻挡节点纹理失败");
            }
        });

        this.maskNode!.addChild(this.blockBgNode);
    }

    /** 更新背景阻挡节点大小 */
    private updateBlockBgSize(): void {
        if (this.blockBgNode) {
            const transform = this.blockBgNode.getComponent(UITransform)!;
            transform.width = this.canvasUITransform.width * 10;
            transform.height = this.canvasUITransform.height * 10;
        }
    }

    /** 创建遮罩节点 */
    private createMaskNode(): void {
        this.maskNode = new Node("Mask Node");
        this.maskNode.layer = 1 << 25;
        this.maskNode.active = false;
        this.maskNode.addComponent(UITransform);
        this.maskNode.addComponent(Mask);
        this.node.addChild(this.maskNode);
    }

    /** 更新引导标志大小 */
    private updateGuideSignSize(): void {
        if (this.guideSignSpriteNode && this.maskNode) {
            const signTransform = this.guideSignSpriteNode.getComponent(UITransform)!;
            const maskTransform = this.maskNode.getComponent(UITransform)!;
            signTransform.setContentSize(maskTransform.width * 1.1, maskTransform.height * 1.1);
        }
    }
    //#endregion

    /** 设置引导数据 */
    public setGuideData(guideData: GuideData): void {
        this.guideData = guideData;
        if (guideData.guideSignType === GuideSignType.FRAME) {
            this.guideSignSpriteNode!.getComponent(Sprite)!.type = Sprite.Type.SLICED;
            this.updateGuideSignSize();
        }
    }

    /**
     * 显示引导
     * @param step 引导步骤（从1开始）
     * @param autoNext 是否自动进入下一步
     * @param textPos 文本位置
     */
    public showGuide(step: number = 1, autoNext: boolean = false, textPos: TextPosition = "follow"): void {
        if (!this.guideData) {
            console.error("引导数据未设置");
            return;
        }

        if (step > this.guideData.nodesAndTexts.length) {
            return;
        }

        const guideItem = this.guideData.nodesAndTexts[step - 1];

        if (guideItem.guideType === GuideType.ONLY_TEXT) {
            this.showOnlyTextGuide(step, autoNext, textPos);
            return;
        }

        this.currentStepTemp = step;
        this.textPosTemp = textPos;

        if (guideItem.guideType === GuideType.TOUCH) {
            const nodePath = guideItem.path!;
            if (StringUtil.isEmpty(nodePath)) {
                console.error(`节点路径为空`);
                return;
            }

            const targetNode = find(nodePath);
            if (!targetNode) {
                console.error(`未找到路径为${nodePath}的节点`);
                return;
            }

            this.showNodeAndTextGuide(targetNode, step, autoNext, textPos);
        } else {
            const keys = guideItem.key!.split("+");
            this.showKeyAndTextGuide(keys, step, autoNext, textPos);
        }
    }

    /**
     * 显示按键和文本引导
     * @param keys 按键数组
     * @param step 引导步骤
     * @param autoNext 是否自动进入下一步
     * @param textPos 文本位置
     */
    private showKeyAndTextGuide(keys: string[], step: number, autoNext: boolean, textPos: TextPosition): void {
        if (!this.maskNode || !this.guideTextBgSpriteNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        let lastKeyTime = 0;
        const pressedKeys: string[] = [];

        const self = this;
        const keyHandler = (event: EventKeyboard) => {
            const currentTime = new Date().getTime();
            if (currentTime - lastKeyTime > self.keyDownTimeGap) {
                lastKeyTime = currentTime;
                pressedKeys.length = 0;
            }

            pressedKeys.push(event.keyCode.toString());

            if (JSON.stringify(keys) === JSON.stringify(pressedKeys)) {
                self.hideGuide();
                if (autoNext) {
                    self.showGuide(step + 1, autoNext, textPos);
                }
                input.off(Input.EventType.KEY_DOWN, keyHandler, self);
            }
        };

        input.on(Input.EventType.KEY_DOWN, keyHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;
        const maskTransform = this.maskNode.getComponent(UITransform)!;
        maskTransform.setContentSize(0, 0);

        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /**
     * 显示节点和文本引导
     * @param targetNode 目标节点
     * @param step 引导步骤
     * @param autoNext 是否自动进入下一步
     * @param textPos 文本位置
     */
    private showNodeAndTextGuide(targetNode: Node, step: number, autoNext: boolean, textPos: TextPosition): void {
        if (!this.maskNode || !this.guideSignSpriteNode || !this.guideTextBgSpriteNode) return;

        this.maskNode.active = true;
        this.guideSignSpriteNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        const self = this;
        const touchHandler = () => {
            self.hideGuide();
            if (autoNext) {
                self.showGuide(step + 1, autoNext, textPos);
            }
            targetNode.off(Node.EventType.TOUCH_START, touchHandler, self);
        };

        targetNode.on(Node.EventType.TOUCH_START, touchHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;

        const maskTransform = this.maskNode.getComponent(UITransform)!;
        const targetTransform = targetNode.getComponent(UITransform)!;
        maskTransform.setContentSize(targetTransform.width * targetNode.scale.x * 1.1, targetTransform.height * targetNode.scale.y * 1.1);

        // 设置遮罩位置
        if (targetNode.parent == this._canvasNode) {
            this.maskNode.setPosition(targetNode.position);
        } else {
            const parentPositions = this.getAllParentPosIncludingSelf(targetNode);
            let totalPos = new Vec3(0, 0, 0);
            for (let i = parentPositions.length - 1; i >= 0; i--) {
                totalPos = totalPos.add(parentPositions[i]);
            }
            this.maskNode.setPosition(totalPos);
        }

        this.adjustGuideSignSpriteNode(step);
        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /** 调整引导标志精灵节点 */
    private adjustGuideSignSpriteNode(step: number): void {
        if (!this.guideSignSpriteNode || !this.maskNode) return;

        if (this.guideData!.guideSignType === GuideSignType.FRAME) {
            this.guideSignSpriteNode.setPosition(this.maskNode.position);
            const signTransform = this.guideSignSpriteNode.getComponent(UITransform)!;
            const maskTransform = this.maskNode.getComponent(UITransform)!;
            signTransform.setContentSize(maskTransform.width * 1.1, maskTransform.height * 1.1);
        } else if (this.guideData!.guideSignType === GuideSignType.HAND) {
            const maskTransform = this.maskNode.getComponent(UITransform)!;

            if (maskTransform.width && maskTransform.height) {
                this.guideSignSpriteNode.active = true;
            } else {
                this.guideSignSpriteNode.active = false;
            }

            if (this.maskNode.position.y >= 0) {
                this.guideSignSpriteNode.setRotationFromEuler(0, 0, 90);
                this.guideSignSpriteNode.setPosition(new Vec3(this.maskNode.position).subtract3f(0, maskTransform.height * 1.01, 0));
            } else {
                this.guideSignSpriteNode.setRotationFromEuler(0, 0, -90);
                this.guideSignSpriteNode.setPosition(new Vec3(this.maskNode.position).subtract3f(0, -maskTransform.height * 1.01, 0));
            }
        }
    }

    /** 获取包括自身在内的所有父节点位置 */
    private getAllParentPosIncludingSelf(node: Node): Vec3[] {
        const positions: Vec3[] = [node.position];
        let current = node;

        while (true) {
            const parent = current.parent;
            if (!parent || parent == this._canvasNode) {
                break;
            }
            positions.push(parent.position);
            current = parent;
        }

        return positions;
    }

    /**
     * 显示仅文本引导
     * @param step 引导步骤
     * @param autoNext 是否自动进入下一步
     * @param textPos 文本位置
     */
    private showOnlyTextGuide(step: number, autoNext: boolean, textPos: TextPosition): void {
        if (!this.maskNode || !this.guideTextBgSpriteNode || !this.blockBgNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        const self = this;
        const touchHandler = () => {
            self.hideGuide();
            if (autoNext) {
                self.showGuide(step + 1, autoNext, textPos);
            }
            self.blockBgNode!.off(Node.EventType.TOUCH_START, touchHandler, self);
        };

        this.blockBgNode.on(Node.EventType.TOUCH_START, touchHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;
        const maskTransform = this.maskNode.getComponent(UITransform)!;
        maskTransform.setContentSize(0, 0);

        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /**
     * 调整引导文本标签节点
     * @param step 引导步骤
     */
    private adjustGuideTextLabelNode(step: number): void {
        if (!this.guideTextLabelNode || !this.guideTextBgSpriteNode) return;

        const label = this.guideTextLabelNode.getComponent(Label)!;
        label.string = this.guideData!.nodesAndTexts[step - 1].guideText;

        const labelTransform = this.guideTextLabelNode.getComponent(UITransform)!;
        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        const bgScale = this.guideTextBgSpriteNode.scale;

        labelTransform.setContentSize(bgTransform.width * bgScale.x / 1.1, bgTransform.height * bgScale.y / 1.1);
    }

    /**
     * 调整引导文本背景精灵节点
     * @param step 引导步骤
     * @param textPos 文本位置
     */
    private adjustGuideTextBgSpriteNode(step: number, textPos: TextPosition): void {
        if (!this.guideTextBgSpriteNode || !this.maskNode) return;

        const maskTransform = this.maskNode.getComponent(UITransform)!;

        if (textPos === "center") {
            this.setGuideTextBgSpriteSizeByTextLength();
            this.guideTextBgSpriteNode.setPosition(0, 0, 0);
        } else if (textPos === "bottom") {
            this.setGuideTextBgSpriteSizeByCanvasSize();
            const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
            const posY = this.canvasUITransform.height / 2 - bgTransform.height / 2;
            this.guideTextBgSpriteNode.setPosition(0, -posY, 0);
        } else if (textPos === "follow") {
            this.setGuideTextBgSpriteSizeByTextLength();

            const maskPos = this.maskNode.position;
            if (maskPos.x >= 0 && maskPos.y >= 0) {
                this.guideTextBgSpriteNode.setPosition(new Vec3(maskPos).subtract3f(maskTransform.width * 1.1, maskTransform.height * 1.1, 0));
            } else if (maskPos.x >= 0 && maskPos.y <= 0) {
                this.guideTextBgSpriteNode.setPosition(new Vec3(maskPos).subtract3f(maskTransform.width * 1.1, -maskTransform.height * 1.1, 0));
            } else if (maskPos.x <= 0 && maskPos.y >= 0) {
                this.guideTextBgSpriteNode.setPosition(new Vec3(maskPos).subtract3f(-maskTransform.width * 1.1, maskTransform.height * 1.1, 0));
            } else if (maskPos.x <= 0 && maskPos.y <= 0) {
                this.guideTextBgSpriteNode.setPosition(new Vec3(maskPos).subtract3f(-maskTransform.width * 1.1, -maskTransform.height * 1.1, 0));
            }
        }
    }

    /** 根据画布大小设置引导文本背景精灵大小 */
    private setGuideTextBgSpriteSizeByCanvasSize(): void {
        if (!this.guideTextBgSpriteNode) return;

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        bgTransform.width = this.canvasUITransform.width;
        bgTransform.height = this.canvasUITransform.height / 7;
    }

    /** 根据文本长度设置引导文本背景精灵大小 */
    private setGuideTextBgSpriteSizeByTextLength(): void {
        if (!this.guideTextBgSpriteNode) return;


        const guideText = this.guideData?.nodesAndTexts[this.currentStepTemp - 1].guideText || "";
        let width = 0;
        let height = 0;

        if (guideText.length <= 15 && guideText.length > 0) {
            width = this.canvasUITransform.width / 7;
            height = this.canvasUITransform.height / 10;
        } else if (guideText.length > 15 && guideText.length <= 40) {
            width = this.canvasUITransform.width / 5;
            height = this.canvasUITransform.height / 8;
        } else if (guideText.length > 40) {
            width = this.canvasUITransform.width / 3;
            height = this.canvasUITransform.height / 6;
        }

        // 确保宽度大于高度
        if (width < height) {
            const temp = height;
            height = width;
            width = temp;
        }

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        bgTransform.setContentSize(width, height);
    }

    /** 隐藏引导 */
    private hideGuide(): void {
        if (this.maskNode) this.maskNode.active = false;
        if (this.guideSignSpriteNode) this.guideSignSpriteNode.active = false;
        if (this.guideTextBgSpriteNode) this.guideTextBgSpriteNode.active = false;
    }

    // ---------------------------------------------------------------------------
    /**
     * 设置引导标志缩放
     */
    setGuideSignScale(scaleX: number, scaleY: number): void {
        if (this.guideSignSpriteNode) {
            this.guideSignSpriteNode.setScale(scaleX, scaleY, 0);
        }
    }

    /**
     * 设置引导文本颜色
     */
    setGuideTextColor(color: Color): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.color = color;
        }
    }

    /**
     * 设置引导文本字体大小
     */
    setGuideTextFontSize(fontSize: number): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.fontSize = fontSize;
        }
    }

    /**
     * 设置引导文本行高
     */
    setGuideTextLineHeight(lineHeight: number): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.lineHeight = lineHeight;
        }
    }

    /**
     * 设置引导文本背景缩放
     */
    setGuideTextBackgroundScale(scaleX: number, scaleY: number): void {
        if (this.guideTextBgSpriteNode && this.guideTextLabelNode) {
            this.guideTextBgSpriteNode.setScale(scaleX, scaleY, 0);
            this.guideTextLabelNode.setScale(1 / scaleX, 1 / scaleY, 0);

            const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
            const labelTransform = this.guideTextLabelNode.getComponent(UITransform)!;
            labelTransform.setContentSize(
                bgTransform.width * scaleX / 1.1,
                bgTransform.height * scaleY / 1.1
            );
        }
    }

    /**
     * 设置遮罩颜色
     */
    setMaskColor(color: Color): void {
        if (this.blockBgNode) {
            const sprite = this.blockBgNode.getComponent(Sprite)!;
            sprite.color = color;
        }
    }

    /**
     * 设置按键间隔时间
     */
    setKeyDownTimeGap(timeGap: number): void {
        this.keyDownTimeGap = timeGap;
    }

    /**
     * 根据UUID获取节点
     */
    private getNodeByUuid(parent: Node, uuid: string): Node | null {
        for (let i = 0; i < parent.children.length; i++) {
            const child = parent.children[i];
            if (child.uuid === uuid) {
                return child;
            }

            if (child.children.length > 0) {
                const result = this.getNodeByUuid(child, uuid);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    }

}