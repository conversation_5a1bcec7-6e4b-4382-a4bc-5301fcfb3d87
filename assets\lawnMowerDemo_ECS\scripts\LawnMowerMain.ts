import { _decorator, Component } from 'cc';
import { Core } from 'db://framework/Core';
import { Debug } from 'db://framework/core/logger/Debug';
import { GameScene } from './GameScene';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-08-01 22:07
 * @filePath assets\lawnMowerDemo_ECS\scripts\LawnMowerMain.ts
 * @description 
 */
@ccclass('LawnMowerMain')
export class LawnMowerMain extends Component {

    @property({ tooltip: "是否启用调试模式" })
    private isDebug: boolean = true;

    private isInitialized: boolean = false;

    protected start(): void {
        this.initializedECS();
    }

    private initializedECS(): void {
        if (this.isInitialized) return;
        try {
            if (this.isDebug) {
                Core.create({
                    debugConfig: {
                        enabled: true,
                        websocketUrl: 'ws://localhost:8080/ecs-debug',
                        autoReconnect: true,
                        channels: {
                            entities: true,
                            systems: true,
                            performance: true,
                            components: true,
                            scenes: true
                        }
                    }
                });
            } else {
                Core.create(false);
            }
            Core.scene = new GameScene();
            this.isInitialized = true;
        } catch (error) {
            Debug.error("ECS框架初始化失败:", error);
        }
    }

    protected update(dt: number): void {
        if (!this.isInitialized) return;
        Core.update(dt);
    }

    protected onDestroy(): void {
        if (this.isInitialized) {
            Debug.log("🧹 清理ECS框架...");
            this.isInitialized = false;
        }
    }

}