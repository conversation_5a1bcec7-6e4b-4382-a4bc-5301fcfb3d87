import { _decorator, Component, math, UITransform, Vec3, view } from 'cc';
const { ccclass, property } = _decorator;

interface ScreenBounds {
    width: number;
    height: number;
}

@ccclass('Monster')
export class Monster extends Component {
    // 移动相关属性
    private initialSpeed: number = 700;
    private minSpeed: number = 650;
    private maxSpeed: number = 1200;
    
    // 私有属性
    private _direction: Vec3 = new Vec3();
    private _speed: number = 0;
    private _screenBounds: ScreenBounds = { width: 0, height: 0 };
    private _uiTransform: UITransform | null = null;

    onEnable() {
        // 获取或添加 UITransform 组件
        this._uiTransform = this.node.getComponent(UITransform);
        if (!this._uiTransform) {
            this._uiTransform = this.node.addComponent(UITransform);
        }

        // 初始化屏幕边界
        const visibleSize = view.getVisibleSize();
        this._screenBounds.width = visibleSize.width;
        this._screenBounds.height = visibleSize.height;

        // 设置初始速度
        this._speed = this.initialSpeed;
        this.setRandomDirection();
    }

    update(deltaTime: number) {
        if (!this._direction || !this._uiTransform) return;

        // 计算移动距离
        const moveDistance = this._speed * deltaTime;
        const moveVector = new Vec3(
            this._direction.x * moveDistance,
            this._direction.y * moveDistance,
            0
        );

        // 计算新位置
        const newPosition = new Vec3(
            this.node.position.x + moveVector.x,
            this.node.position.y + moveVector.y,
            this.node.position.z
        );

        // 获取节点尺寸
        const halfWidth = this._uiTransform.width / 2;
        const halfHeight = this._uiTransform.height / 2;

        // 碰撞检测和反弹
        let hasCollision = false;

        // 水平边界检测
        if (newPosition.x - halfWidth < -this._screenBounds.width / 2) {
            newPosition.x = -this._screenBounds.width / 2 + halfWidth;
            this._direction.x = -this._direction.x;
            hasCollision = true;
        } else if (newPosition.x + halfWidth > this._screenBounds.width / 2) {
            newPosition.x = this._screenBounds.width / 2 - halfWidth;
            this._direction.x = -this._direction.x;
            hasCollision = true;
        }

        // 垂直边界检测
        if (newPosition.y - halfHeight < -this._screenBounds.height / 2) {
            newPosition.y = -this._screenBounds.height / 2 + halfHeight;
            this._direction.y = -this._direction.y;
            hasCollision = true;
        } else if (newPosition.y + halfHeight > this._screenBounds.height / 2) {
            newPosition.y = this._screenBounds.height / 2 - halfHeight;
            this._direction.y = -this._direction.y;
            hasCollision = true;
        }

        // 如果发生碰撞，改变速度
        if (hasCollision) {
            this.changeSpeed();
        }

        // 更新位置
        this.node.position = newPosition;
    }

    private changeSpeed(): void {
        // 在最小和最大速度之间随机选择新速度
        this._speed = math.randomRange(this.minSpeed, this.maxSpeed);
    }

    public setRandomDirection(): void {
        // 生成随机角度
        const randomAngle = math.randomRange(0, 2 * Math.PI);
        
        // 设置方向向量
        this._direction.x = Math.cos(randomAngle);
        this._direction.y = Math.sin(randomAngle);
        this._direction.z = 0;
        
        // 标准化方向向量
        Vec3.normalize(this._direction, this._direction);
    }
}
