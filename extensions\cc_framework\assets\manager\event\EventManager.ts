import { _decorator } from 'cc';
import { BaseManager } from '../../base/BaseManager';
import { ListenerFunc } from './EventMessage';
const { ccclass, property } = _decorator;

class EventData {
    public event!: string;
    public listener!: ListenerFunc;
    public object: any;
}

// /** 批量注册、移除全局事件对象 */
// export class MessageEventData {
//     private events: Map<string, Array<EventData>> = new Map();

//     /**
//      * 注册全局事件
//      * @param event      事件名
//      * @param listener   处理事件的侦听器函数
//      * @param object     侦听函数绑定的作用域对象
//      */
//     on(event: string, listener: ListenerFunc, object: object) {
//         let eds = this.events.get(event);
//         if (eds == null) {
//             eds = [];
//             this.events.set(event, eds);
//         }
//         let ed: EventData = new EventData();
//         ed.event = event;
//         ed.listener = listener;
//         ed.object = object;
//         eds.push(ed);

//         message.on(event, listener, object);
//     }

//     /**
//     * 移除全局事件
//      * @param event     事件名
//      */
//     off(event: string) {
//         let eds = this.events.get(event);
//         if (!eds) return;

//         for (let eb of eds) {
//             message.off(event, eb.listener, eb.object);
//         }
//         this.events.delete(event);
//     }

//     /** 
//      * 触发全局事件 
//      * @param event      事件名
//      * @param args       事件参数
//      */
//     dispatchEvent(event: string, ...args: any) {
//         message.dispatchEvent(event, ...args);
//     }

//     /** 清除所有的全局事件监听 */
//     clear() {
//         const keys = Array.from(this.events.keys());
//         for (let event of keys) {
//             this.off(event)
//         }
//     }
// }
/**
 * <AUTHOR>
 * @data 2025-03-13 16:05
 * @filePath assets\core\manager\event\EventManager.ts
 * @description 事件管理器 - 负责管理游戏中的全局事件系统
 * 提供事件的注册、监听、触发和移除等功能
 */
@ccclass('EventManager')
export class EventManager extends BaseManager {
    /** 事件数据存储 - 使用Map存储所有注册的事件及其监听器 */
    private events: Map<string, Array<EventData>> = new Map();

    /**
     * 组件加载时调用
     * 初始化事件管理器
     */
    protected onLoad(): void {
        this.log("事件管理器初始化完成");
    }

    /**
     * 注册全局事件
     * @param event      事件名称
     * @param listener   事件监听器函数
     * @param object     监听器函数绑定的作用域对象
     * @example
     * // 注册一个事件监听
     * app.event.on("gameStart", this.onGameStart, this);
     */
    public on(event: string, listener: ListenerFunc, object: object): void {
        if (!event || !listener) {
            this.warn(`注册【${event}】事件的侦听器函数为空`);
            return;
        }

        let eds = this.events.get(event);
        if (eds == null) {
            eds = [];
            this.events.set(event, eds);
        }

        let length = eds.length;
        for (let i = 0; i < length; i++) {
            let bin = eds[i];
            if (bin.listener == listener && bin.object == object) {
                this.warn(`名为【${event}】的事件重复注册侦听器`);
            }
        }

        let data: EventData = new EventData();
        data.event = event;
        data.listener = listener;
        data.object = object;
        eds.push(data);
    }

    /**
     * 监听一次事件，事件响应后自动移除监听
     * @param event     事件名称
     * @param listener  事件触发回调方法
     * @param object    监听器函数绑定的作用域对象
     * @example
     * // 监听一次事件
     * app.event.once("gameOver", this.onGameOver, this);
     */
    public once(event: string, listener: ListenerFunc, object: object): void {
        let _listener: any = ($event: string, ...$args: any) => {
            this.off(event, _listener, object);
            _listener = null;
            listener.call(object, $event, $args);
        }
        this.on(event, _listener, object);
    }

    /**
     * 移除全局事件监听
     * @param event     事件名称
     * @param listener  事件监听器函数
     * @param object    监听器函数绑定的作用域对象
     * @example
     * // 移除事件监听
     * app.event.off("gameStart", this.onGameStart, this);
     */
    public off(event: string, listener: Function, object: object): void {
        let eds = this.events.get(event);

        if (!eds) {
            this.log(`名为【${event}】的事件不存在`);
            return;
        }

        let length = eds.length;
        for (let i = 0; i < length; i++) {
            let bin: EventData = eds[i];
            if (bin.listener == listener && bin.object == object) {
                eds.splice(i, 1);
                break;
            }
        }

        if (eds.length == 0) {
            this.events.delete(event);
        }
    }

    /** 
     * 触发全局事件
     * @param event      事件名称
     * @param args       事件参数
     * @example
     * // 触发一个事件
     * app.event.dispatchEvent("gameStart", { level: 1 });
     */
    public dispatchEvent(event: string, ...args: any): void {
        let list = this.events.get(event);

        if (list != null) {
            let eds: Array<EventData> = list.concat();
            let length = eds.length;
            for (let i = 0; i < length; i++) {
                let eventBin = eds[i];
                eventBin.listener.call(eventBin.object, event, ...args);
            }
        }
    }
}