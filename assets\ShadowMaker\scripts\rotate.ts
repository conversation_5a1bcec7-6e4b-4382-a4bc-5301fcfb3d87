import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 旋转组件
 * 用于实现节点的持续旋转效果
 */
@ccclass('rotate')
export class Rotate extends Component {
    /**
     * 是否顺时针旋转
     */
    @property
    public isClockwise: boolean = true;

    /**
     * 旋转速度
     */
    @property
    public rotationSpeed: number = 5;

    /**
     * 组件启动时调用
     */
    protected start(): void {
        // 初始化逻辑可以在这里添加
    }

    /**
     * 每帧更新
     * @param deltaTime 帧间隔时间
     */
    protected update(deltaTime: number): void {
        if (this.isClockwise) {
            this.node.angle += this.rotationSpeed;
        } else {
            this.node.angle -= this.rotationSpeed;
        }
    }
} 