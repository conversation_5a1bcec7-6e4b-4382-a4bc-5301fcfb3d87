/**
 * <AUTHOR>
 * @data 2025-07-08 10:54
 * @filePath extensions\cc_framework\assets\lib\excel\TableBase.ts
 * @description 
 */
export class TableBase {

    /** 表数据 */
    protected data: Record<string, any>;

    /** KEY */
    public key: string = '';

    /**
     * 初始化数据
     * @param key 具体某个key值
     * @param table 对应的json数据对象
     */
    constructor(key: string, table: Record<string, any>) {
        this.key = key;
        this.data = table[key];
    }
    /**
     * 获取匹配数据
     * @param key 键
     * @returns 匹配数据
     */
    public getMatchingData(key: string): any {
        return this.data[key];
    }

}