/**
 * <AUTHOR>
 * @data 2025-03-13 16:24
 * @filePath assets\core\manager\timer\Timer.ts
 * @description 定时触发组件 - 用于实现定时器功能
 * 支持定时触发回调、进度计算、重置和停止等功能
 */
export class Timer {
    /** 定时器回调函数 - 在定时器触发时执行 */
    private callback: Function | null = null;

    /** 已逝去的时间（秒） */
    private _elapsedTime: number = 0;

    /**
     * 获取已逝去的时间
     * @returns 已逝去的时间（秒）
     */
    public get elapsedTime(): number {
        return this._elapsedTime;
    }

    /** 触发间隔时间（秒） */
    private _step: number = -1;

    /**
     * 获取触发间隔时间
     * @returns 触发间隔时间（秒）
     */
    public get step(): number {
        return this._step;
    }

    /**
     * 设置触发间隔时间
     * @param step 新的触发间隔时间（秒）
     * @description 设置新的间隔时间时会重置已逝去的时间
     */
    public set step(step: number) {
        this._step = step;                     // 设置新的间隔时间
        this._elapsedTime = 0;                 // 重置已逝去的时间
    }

    /**
     * 获取定时器进度
     * @returns 当前进度（0-1之间的值）
     * @description 返回当前已逝去时间占总间隔时间的比例
     */
    public get progress(): number {
        return this._elapsedTime / this._step;
    }

    /**
     * 定时器构造函数
     * @param step 触发间隔时间（秒）
     * @description 创建一个新的定时器实例
     */
    constructor(step: number) {
        this.step = step;
    }

    /**
     * 更新定时器状态
     * @param dt 帧间隔时间（秒）
     * @returns 是否触发回调
     * @description 更新定时器状态，当达到触发间隔时执行回调
     */
    public update(dt: number): boolean {
        if (this.step <= 0) return false;

        this._elapsedTime += dt;

        if (this._elapsedTime >= this._step) {
            this._elapsedTime -= this._step;
            this.callback?.call(this);
            return true;
        }
        return false;
    }

    /**
     * 重置定时器
     * @description 将已逝去的时间重置为0，保持当前间隔时间不变
     */
    public reset(): void {
        this._elapsedTime = 0;
    }

    /**
     * 停止定时器
     * @description 停止定时器，重置已逝去的时间并将间隔时间设为-1
     */
    public stop(): void {
        this._elapsedTime = 0;
        this.step = -1;
    }
}