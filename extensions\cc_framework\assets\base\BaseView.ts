import { _decorator, Component, Enum, js, Node, tween, UIOpacity, UITransform, v3, Widget } from 'cc';
import { DEV, EDITOR } from 'cc/env';
import { APP } from '../core/APP';
import { Debug, ILog } from '../core/logger/Debug';
import { ListenerFunc } from '../manager/event/EventMessage';
import { LayerType, UIViewShowType } from '../manager/ui/Defines';
const { ccclass, property } = _decorator;

/**
 * UI视图基类
 * @description 所有UI界面的基类，提供基础的UI功能
 * 包括窗口层级管理、遮罩控制、安全区域适配、日志打印等功能
 * 支持分帧加载滚动视图元素，优化性能
 */
@ccclass('BaseView')
export class BaseView extends Component {
    /** 窗口层级类型 */
    @property({ type: Enum(LayerType), displayName: "窗口层级" })
    public layer: LayerType = LayerType.UI;
    /** 是否点击空白区域关闭窗口 */
    @property({ displayName: "触摸非窗口区域关闭", visible: function (this: BaseView) { return this.layer == LayerType.Pop || this.layer == LayerType.Top } })
    public vacancy: boolean = false;
    /** 是否显示背景遮罩 */
    @property({ displayName: "显示背景遮罩", visible: function (this: BaseView) { return this.layer == LayerType.Pop || this.layer == LayerType.Top } })
    public mask: boolean = false;
    /** 是否启用安全区域适配 */
    @property({ displayName: "真机安全区域显示", visible: function (this: BaseView) { return this.layer == LayerType.Pop || this.layer == LayerType.Top } })
    public safeArea: boolean = false;
    @property({ type: Enum(UIViewShowType), displayName: "动画类型" })
    private showType: UIViewShowType = UIViewShowType.Normal;
    @property({ displayName: "动画持续时间", slide: true, range: [0.1, 1, 0.1], visible: function (this: BaseView) { return this.showType != UIViewShowType.Normal } })
    private actTime: number = 0.2;

    /** 当前视图名称，自动获取类名 */
    private _base_view_name: string = js.getClassName(this);
    /** 外部传入的参数 */
    protected params: any = null;
    /** 当前UI的ID用于节点直接关闭当前框口 */
    private uiId: number = 0;
    /** 事件映射表 */
    private _eventMap: Map<string, { callBack: Function, target: any }> = new Map();
    /**
     * 编辑器中的重置方法
     * @description 在编辑器中自动添加UITransform和Widget组件，并设置默认属性
     * @param didResetToDefault 是否重置为默认值
     */
    public resetInEditor(didResetToDefault?: boolean): void {
        if (EDITOR) {
            this.node.getComponent(UITransform) || this.node.addComponent(UITransform);

            const widget = this.node.getComponent(Widget) || this.node.addComponent(Widget);
            widget.isAlignBottom = true;
            widget.isAlignLeft = true;
            widget.isAlignRight = true;
            widget.isAlignTop = true;
            widget.top = 0;
            widget.left = 0;
            widget.right = 0;
            widget.bottom = 0;
            widget.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;
        }
    }
    //#region 日志相关
    /** 
     * 日志打印方法
     * @description 开发环境下使用带颜色的格式化日志，生产环境使用普通日志
     */
    protected get log(): ILog { return Debug.create('log', '#1e90ff', DEV ? `[${this._base_view_name}] LOG` : `[${this._base_view_name}] [LOG]`); }
    /** 
     * 警告打印方法
     * @description 开发环境下使用带颜色的格式化警告，生产环境使用普通警告
     */
    protected get warn(): ILog { return Debug.create('warn', '#ff7f50', DEV ? `[${this._base_view_name}] WARN` : `[${this._base_view_name}] [WARN]`); }
    /** 
     * 错误打印方法
     * @description 开发环境下使用带颜色的格式化错误，生产环境使用普通错误
     */
    protected get error(): ILog { return Debug.create('error', '#ff4757', DEV ? `[${this._base_view_name}] ERROR` : `[${this._base_view_name}] [ERROR]`); }
    //#endregion
    //#region 节点变动相关
    /**
     * 节点添加到层级后的回调
     * @description 初始化UI参数，设置关闭按钮事件，调用onOpen方法
     * @param params 外部传递的参数，包含uiId和uiArgs
     */
    private onAdded(params: { uiId: number, uiArgs: any }): void {
        this.params = params.uiArgs;
        this.uiId = params.uiId;
        const btn = this.node.getChildByName("closeBtn");
        if (btn) {
            btn.off(Node.EventType.TOUCH_END);
            // 不需要每次都监听，只需要监听一次
            btn.on(Node.EventType.TOUCH_END, this.onCloseView, this);
        }
        this.showOpenAnim();
        this.addEventListener();
        this.onOpen();
    }
    /** 显示打开动画 */
    private showOpenAnim(): void {
        const root = this.node.getChildByName("root") || this.node;
        if (this.showType === UIViewShowType.Scale) {
            const originalScale = root.scale;
            root.setScale(v3(0.1, 0.1, 0.1));
            tween(root)
                .to(this.actTime, { scale: originalScale })
                // .call(() => {
                //     this._onOpenAnimOver();
                // })
                .start();
        } else if (this.showType === UIViewShowType.Opacity) {
            const opacity = root.getComponent(UIOpacity) || root.addComponent(UIOpacity);
            opacity.opacity = 0;
            tween(opacity)
                .to(this.actTime, { opacity: 255 })
                // .call(() => {
                //     this._onOpenAnimOver();
                // })
                .start();
            // } else {
            //     this._onOpenAnimOver();
        }
    }
    // /**
    //  * 隐藏其他视图
    //  * 建议方法名：hideOtherViews
    //  */
    // protected _hideOtherViews(): void {
    //     const views = UIManager.getAllViews();
    //     const currentIndex = views.indexOf(this);
    //     for (let i = 0; i < views.length; i++) {
    //         const view = views[i];
    //         const node = view.node;
    //         const zIndex = node.zIndex;
    //         if (view !== this && node.active && currentIndex > zIndex) {
    //             view.node.active = false;
    //             this._hideViews.push(view);
    //         }
    //     }
    // }
    // /**
    //  * 显示隐藏的视图
    //  * 建议方法名：showHiddenViews
    //  */
    // showHideViews(): void {
    //     if (this._hideViews && this._hideViews.length > 0) {
    //         for (let i = 0; i < this._hideViews.length; i++) {
    //             const view = this._hideViews[i];

    //             if (view && view.node) {
    //                 view.node.active = true;
    //             }
    //         }
    //     }
    // }
    /** 
     * 关闭窗口
     * @description 调用UI管理器关闭当前窗口
     */
    protected onCloseView(): void { APP.ui.closeByNode(this.node, false) }
    /** 
     * 打开窗口
     * @description 每次打开页面会调用的函数方法，子类可以重写此方法实现自定义逻辑
     */
    protected onOpen(): void { }
    /** 
     * 窗口移除前的回调
     * @description 如果指定onBeforeRemoved，则next必须调用，否则节点不会被正常删除
     * 例如：希望节点做一个FadeOut然后删除，则可以在onBeforeRemoved中播放action动画，动画结束后调用next
     * @param next 回调方法，用于通知移除完成
     */
    public onBeforeRemove?(next: Function): void;

    /** 
     * 注册事件
     * @description 每次打开页面会调用的函数方法，如果使用addEvent方法注册事件，则不需要关系收回问题。在页面关闭时会自动移除所有事件
     */
    protected addEventListener(): void { }
    /**
     * 注册全局事件
     * @param event      事件名称
     * @param listener   事件监听器函数
     * @param object     监听器函数绑定的作用域对象
     */
    protected addEvent(event: string, listener: ListenerFunc, object?: object): void {
        if (!object) { object = this; }

        const existingEvent = this._eventMap.get(event);
        if (existingEvent && existingEvent.target === object && existingEvent.callBack === listener) {
            this.warn(`名为【${event}】的事件重复注册侦听器`);
            return;
        }
        APP.event.on(event, listener, object);
        this._eventMap.set(event, { callBack: listener, target: object });
    }
    /** 移除所有事件 */
    private removeEvent(): void {
        this._eventMap.forEach((value, key) => {
            APP.event.off(key, value.callBack, value.target);
        });
        this._eventMap.clear();
    }
    //#endregion
    // //#region 异步分帧加载, 后续移除
    // /**
    //  * 分帧加载滚动视图元素
    //  * @description 优化滚动视图性能，将大量节点的创建和初始化分散到多帧中执行
    //  * @param scrollView 滚动视图组件
    //  * @param dataSource 数据源，可以是可迭代对象或类数组对象
    //  * @param initItem 初始化每个项目的回调函数
    //  * @param poolName 对象池名称，如果传入则会在加载前回收对象池中的所有节点
    //  * @param batchSize 每帧处理的节点数量，默认为1
    //  */
    // public asyncLoadScrollViewItem<T>(scrollView: ScrollView, dataSource: Iterable<T> | ArrayLike<T>, initItem: (scrollView: ScrollView, item: T, index: number) => void, poolName?: string, batchSize: number = 1): void {
    //     if (this.isAsyncLoading) {
    //         console.warn('Previous async loading is still in progress');
    //         return;
    //     }

    //     if (!scrollView || !scrollView.content) {
    //         console.error('ScrollView or its content is invalid');
    //         return;
    //     }

    //     this.isAsyncLoading = true;
    //     if (!StringUtil.isEmpty(poolName)) NodePoolUtil.Put(poolName!, scrollView.content!.children);

    //     setTimeout(async () => {
    //         try {
    //             await this.executePreFrame(this._getItemGenerator(scrollView, dataSource, initItem), batchSize);
    //         } finally {
    //             this.isAsyncLoading = false;
    //         }
    //     });
    // }
    // /**
    //  * 分帧执行 Generator 逻辑
    //  * @description 将Generator中的任务分散到多帧中执行，避免单帧卡顿
    //  * @param generator 生成器函数
    //  * @param batchSize 每帧处理的节点数量
    //  * @returns Promise，当所有任务执行完成时resolve
    //  */
    // private executePreFrame(generator: Generator, batchSize: number) {
    //     return new Promise<void>((resolve, reject) => {
    //         let gen = generator;
    //         // 创建执行函数
    //         let execute = () => {
    //             let count = 0;

    //             // 然后一直从 Generator 中获取已经拆分好的代码段出来执行
    //             for (let iter = gen.next(); ; iter = gen.next()) {
    //                 // 判断是否已经执行完所有 Generator 的小代码段，如果是的话，那么就表示任务完成
    //                 if (iter == null || iter.done) {
    //                     resolve();
    //                     return;
    //                 }

    //                 count++;

    //                 // 当处理的节点数达到每帧设定的数量，将剩余工作推迟到下一帧
    //                 if (count >= batchSize) {
    //                     // 开定时器，让下一帧再执行
    //                     this.scheduleOnce(() => {
    //                         execute();
    //                     });
    //                     return;
    //                 }
    //             }
    //         };

    //         // 运行执行函数
    //         execute();
    //     });
    // }
    // /**
    //  * 获取项目生成器
    //  * @description 根据数据源类型创建对应的生成器函数
    //  * @param scrollView 滚动视图组件
    //  * @param dataSource 数据源
    //  * @param initItem 初始化回调函数
    //  * @returns Generator 生成器
    //  */
    // private *_getItemGenerator<T>(scrollView: ScrollView, dataSource: Iterable<T> | ArrayLike<T>, initItem: (scrollView: ScrollView, item: T, index: number) => void) {
    //     if (Symbol.iterator in Object(dataSource)) {
    //         // 处理可迭代对象
    //         let index = 0;
    //         for (const item of dataSource as Iterable<T>) {
    //             yield initItem.call(this, scrollView, item, index++);
    //         }
    //     } else {
    //         // 处理类数组对象
    //         const arrayLike = dataSource as ArrayLike<T>;
    //         for (let i = 0; i < arrayLike.length; i++) {
    //             yield initItem.call(this, scrollView, arrayLike[i], i);
    //         }
    //     }
    // }
    // //#endregion
}