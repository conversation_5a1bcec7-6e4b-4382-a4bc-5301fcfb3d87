import { AssetManager, JsonAsset } from "cc";
import { APP } from "../../core/APP";
import { Debug } from "../../core/logger/Debug";
import { TableBase } from "./TableBase";

// 可扩展接口（项目中通过声明合并扩展）
export interface ExcelTableRegistry { }

type ExcelFileName = keyof ExcelTableRegistry;

/**
 * <AUTHOR>
 * @data 2025-07-07 17:33
 * @filePath extensions\cc_framework\assets\lib\excel\ExcelManager.ts
 * @description 数据表管理类。需要配合cocos-extensions-tools使用
 */
export class ExcelManager {

    /* 单例 */
    private static _instance: ExcelManager = null!;
    public static get inst(): ExcelManager { return ExcelManager._instance || (ExcelManager._instance = new ExcelManager()); }
    private constructor() { }

    private _excelMap: Map<string, Map<string, TableBase>> = new Map();

    /**
     * 加载 Excel 文件
     * @param fileName 表名
     * @param tableEntryClass 表类，需要继承 TableBase
     * @param path 路径 
     * @param callback 回调函数 返回加载的JsonAsset
     * @param bundleName 资源包名 默认是RESOURCES
     */
    public loadExcelFile<K extends ExcelFileName>(
        fileName: K,
        tableEntryClass: new (key: string, table: Record<string, any>) => TableBase,
        path: string,
        callback?: (data: JsonAsset) => void,
        bundleName: string = AssetManager.BuiltinBundleName.RESOURCES
    ): void {
        APP.res.loadAsync(bundleName, path, JsonAsset).then(jsonAsset => {
            if (jsonAsset) {
                this._excelMap.set(fileName, this.convertExcelData(jsonAsset.json, tableEntryClass));
            } else {
                Debug.error(`load excel ${fileName} error`);
            }
            if (callback) callback(jsonAsset);
            jsonAsset.decRef();
        });
    }

    /**
     * 转换 Excel 数据
     * @param dataObj 数据对象
     * @param constructorFn 构造函数
     * @returns 转换后的数据
     */
    private convertExcelData(dataObj: Record<string, any> | null, constructorFn: new (key: string, table: Record<string, any>) => TableBase): Map<string, TableBase> {
        const resultMap = new Map<string, TableBase>();
        for (const key in dataObj) {
            resultMap.set(key, new constructorFn(key, dataObj));
        }
        return resultMap;
    }

    /**
     * 获取 Excel 数据
     * @param fileName 文件名
     * @returns 返回 Excel 数据
     */
    public getExcelDataMap<K extends ExcelFileName>(fileName: K): Map<string, ExcelTableRegistry[K]> {
        if (!this._excelMap.has(fileName)) {
            Debug.error(`get excel ${fileName} error`);
        }
        return this._excelMap.get(fileName) as Map<string, ExcelTableRegistry[K]>;
    }

    /**
     * 获取 Excel 数据
     * @param fileName 文件名
     * @param key 键
     * @returns 返回 Excel 数据
     */
    public getExcelData<K extends ExcelFileName>(fileName: K, key: string): ExcelTableRegistry[K] {
        return this.getExcelDataMap(fileName).get(key) as ExcelTableRegistry[K];
    }

    /**
     * 获取相同数据
     * @param data 数据数组
     * @param key 键
     * @param value 值
     * @param compareKey 比较键
     * @param compareValue 比较值
     * @returns 相同数据数组
     */
    public getMatchingData<K extends ExcelFileName>(fileName: K, key: string, value: any): Array<ExcelTableRegistry[K]> {
        const resultArray: Array<ExcelTableRegistry[K]> = [];
        if (this._excelMap.has(fileName)) {
            const data = this._excelMap.get(fileName)!;
            data.forEach((item) => {
                if (item.getMatchingData(key) === value) {
                    resultArray.push(item as ExcelTableRegistry[K]);
                }
            });
        }
        return resultArray;
    }
}