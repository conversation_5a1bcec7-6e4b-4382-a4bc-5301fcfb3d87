"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.template = exports.$ = void 0;
exports.update = update;
exports.ready = ready;
exports.close = close;
const fs_1 = require("fs");
const path_1 = require("path");
exports.$ = {
    'code': '#code',
    'section': '#section'
};
exports.template = `
<ui-section id="section" header="资源目录说明" :expanded="true" style="transform: translateY(-38px);" expand>
    <ui-code id="code"></ui-code>
</ui-section>
`;
function update(assetList, metaList) {
    this.assetList = assetList;
    this.metaList = metaList;
    if (assetList.length === 0) {
        this.$.code.innerHTML = '';
    }
    else {
        this.$.code.innerHTML = assetList
            .filter((asset) => {
            const mdFile = (0, path_1.join)(asset.file, `.${asset.name}.md`);
            return (0, fs_1.existsSync)(mdFile);
        })
            .map((asset) => {
            const mdFile = (0, path_1.join)(asset.file, `.${asset.name}.md`);
            const mdStr = (0, fs_1.readFileSync)(mdFile, 'utf-8');
            return assetList.length > 1 ? `${asset.url}:\n ${mdStr}` : mdStr;
        })
            .join('\n') || '';
    }
    if (this.$.code.innerHTML === '') {
        this.$.section.hidden = true;
    }
    else {
        this.$.section.hidden = false;
    }
}
function ready() { }
function close() { }
//# sourceMappingURL=data:application/json;base64,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