import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 游戏管理器组件
 * 用于管理不同场景节点的显示和切换
 */
@ccclass('GameManager')
export class GameManager extends Component {
    /**
     * 旋转场景节点
     */
    @property(Node)
    public ndRotation: Node | null = null;

    /**
     * 移动场景节点
     */
    @property(Node)
    public ndMove: Node | null = null;

    /**
     * 自定义场景节点
     */
    @property(Node)
    public ndCustom: Node | null = null;

    /**
     * Spine场景节点
     */
    @property(Node)
    public ndSpine: Node | null = null;

    /**
     * 组件启动时调用
     */
    protected start(): void {
        // 初始化逻辑可以在这里添加
    }

    /**
     * 按钮点击事件处理
     * @param target 点击的按钮节点
     * @param customEventData 自定义事件数据，用于区分不同按钮
     */
    public onButtonClick(target: Node, customEventData: string): void {
        switch (customEventData) {
            case '1':
                this.showRotationScene();
                break;
            case '2':
                this.showMoveScene();
                break;
            case '3':
                this.showCustomScene();
                break;
            case '4':
                this.showSpineScene();
                break;
        }
    }

    /**
     * 显示旋转场景
     */
    private showRotationScene(): void {
        if (this.ndRotation) this.ndRotation.active = true;
        if (this.ndMove) this.ndMove.active = false;
        if (this.ndCustom) this.ndCustom.active = false;
        if (this.ndSpine) this.ndSpine.active = false;
    }

    /**
     * 显示移动场景
     */
    private showMoveScene(): void {
        if (this.ndRotation) this.ndRotation.active = false;
        if (this.ndMove) this.ndMove.active = true;
        if (this.ndCustom) this.ndCustom.active = false;
        if (this.ndSpine) this.ndSpine.active = false;
    }

    /**
     * 显示自定义场景
     */
    private showCustomScene(): void {
        if (this.ndRotation) this.ndRotation.active = false;
        if (this.ndMove) this.ndMove.active = false;
        if (this.ndCustom) this.ndCustom.active = true;
        if (this.ndSpine) this.ndSpine.active = false;
    }

    /**
     * 显示Spine场景
     */
    private showSpineScene(): void {
        if (this.ndRotation) this.ndRotation.active = false;
        if (this.ndMove) this.ndMove.active = false;
        if (this.ndCustom) this.ndCustom.active = false;
        if (this.ndSpine) this.ndSpine.active = true;
    }

    /**
     * 每帧更新
     * @param deltaTime 帧间隔时间
     */
    protected update(deltaTime: number): void {
        // 更新逻辑可以在这里添加
    }
} 