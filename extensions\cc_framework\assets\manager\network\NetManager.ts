import { Debug } from "../../core/logger/Debug";

interface IOptions {
    url: string,
    data?: any,
    method?: 'post' | 'get' | 'put' | 'GET' | 'POST' | 'PUT',
    timeout?: number,
    responseType?: XMLHttpRequestResponseType,
    headers?: { [key in string]: string }
}

interface ICallback {
    (error: string, response?: any, request?: XMLHttpRequest): any
}

/**
 * <AUTHOR>
 * @data 2025-03-20 13:23
 * @filePath assets\core\manager\network\NetManager.ts
 * @description 网络管理器 - 负责处理游戏中的网络请求
 * 提供HTTP请求、数据序列化、错误处理等功能
 */
export class NetManager {
    /** 
     * 支持的二进制数据类型列表
     * 用于判断请求数据是否为二进制类型
     */
    private readonly bufferType = [
        'Blob',
        'ArrayBuffer',
        'Int8Array',        // 1byte  -128 to 127
        'Uint8Array',       // 1byte  0 to 255
        'Uint8ClampedArray', // 1byte  0 to 255
        'Int16Array',       // 2byte  -32768 to 32767
        'Uint16Array',      // 2byte  0 to 65535
        'Int32Array',       // 4byte  -2147483648 to 2147483647
        'Uint32Array',      // 4byte  0 to 4294967295
        'Float32Array',     // 4byte  1.2x10^-38 to 3.4x10^38
        'Float64Array',     // 8byte  5.0x10^-324 to 1.8x10^308
        'BigInt64Array',    // 8byte  -2^63 to (2^63)-1
        'BigUint64Array'    // 8byte  0 to (2^64)-1
    ];

    /**
     * 发送HTTP请求
     * @param option 请求配置
     * @param callback 回调函数
     * @returns XMLHttpRequest实例
     */
    private _request(option: IOptions, callback?: ICallback | null): XMLHttpRequest {
        // 请求类型
        let method = option.method || 'GET';
        // responseType
        let responseType: XMLHttpRequestResponseType = option.responseType || ''; //arraybuffer
        // 超时时间
        let timeout = option.timeout || 5000;
        // 请求头
        let headers = option.headers || { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }; // application/json text/plain
        // 请求网址、数据、数据类型
        let url = '';
        let data = null;
        let buffer = false;
        if (method === 'post' || method === 'POST' || method === 'put' || method === 'PUT') {
            if (option.data) {
                url = option.url;
                data = option.data;
            } else {
                url = option.url.split('?')[0];
                data = this.parseQueryString(option.url);
            }
            buffer = this.isBuffer(data);
        } else {
            if (option.data) {
                if (option.url.indexOf('?') >= 0) {
                    url = option.url + '&' + this.stringifyQueryString(option.data);
                } else {
                    url = option.url + '?' + this.stringifyQueryString(option.data);
                }
            } else {
                url = option.url;
            }
        }

        // 请求实例
        let request = new XMLHttpRequest();
        request.open(method.toLocaleLowerCase(), url, true);

        // responseType 一般为'arraybuffer'或''
        if (responseType) request.responseType = responseType;

        // 设置超时时间
        if (timeout > 0) request.timeout = timeout;

        // 是否是json
        let isJson = buffer ? false : (data !== null && typeof data !== 'string');
        if (isJson && (!option.headers || !option.headers['Content-Type'])) {
            headers['Content-Type'] = 'application/json; charset=UTF-8';
        }

        // 设置请求头
        for (let key in headers) {
            request.setRequestHeader(key, headers[key]);
        }
        Debug.log("NetManager", "发送请求", url, data);
        // 发送请求
        request.send(isJson ? JSON.stringify(data) : data);

        request.onload = function () {
            if (request.status >= 200 && request.status < 400) {
                if (responseType === 'arraybuffer') {
                    callback && callback(null!, request.response, request);
                } else {
                    try {
                        callback && callback(null!, JSON.parse(request.responseText), request);
                    } catch (e) {
                        callback && callback(null!, request.responseText, request);
                    }
                }
            } else {
                callback && callback('status: ' + request.status, request.responseText, request);
            }
            callback = null;
        };

        const ontimeout = function () {
            if (callback) {
                callback('timeout');
                callback = null;
                Debug.error("NetManager", "连接超时", url);
            }
        };
        request.ontimeout = ontimeout;

        request.onerror = function () {
            if (callback) {
                callback('error');
                callback = null;
                Debug.error("NetManager", "连接失败", url);
            }
        };
        request.onabort = function () {
            if (callback) {
                callback('abort');
                callback = null;
                Debug.error("NetManager", "连接终止", url);
            }
        };

        if (timeout) {
            setTimeout(() => {
                if (callback) {
                    ontimeout();
                    request.abort();
                }
            }, timeout);
        }

        return request;
    }

    /**
     * 将对象转换为URL参数字符串
     * @param params 要转换的参数对象
     * @returns URL参数字符串
     * @example
     * // 转换对象为URL参数
     * const params = { name: "test", age: 18 };
     * const queryString = this.stringifyQueryString(params);
     * // 结果: "name=test&age=18"
     */
    private stringifyQueryString(params: any): string {
        if (typeof params === 'string') {
            return params;
        } else if (params === null || typeof params !== 'object') {
            return encodeURIComponent(JSON.stringify(params));
        } else {
            let qs = '';
            for (let i in params) {
                if (params[i] !== undefined) {
                    if (typeof params[i] === 'object') {
                        qs += '&' + i + '=' + encodeURIComponent(JSON.stringify(params[i]));
                    } else {
                        qs += '&' + i + '=' + encodeURIComponent(params[i]);
                    }
                }
            }
            return qs.slice(1);
        }
    }

    /**
     * 判断参数是否为二进制类型
     * @param param 要判断的参数
     * @returns 是否为二进制类型
     */
    private isBuffer(param: any): boolean {
        const type = Object.prototype.toString.call(param).slice(8, -1);
        return this.bufferType.indexOf(type) > -1;
    }

    /**
     * 解析URL参数字符串为对象
     * @param queryStr URL参数字符串
     * @returns 解析后的参数对象
     * @example
     * // 解析URL参数
     * const queryStr = "name=test&age=18";
     * const params = this.parseQueryString(queryStr);
     * // 结果: { name: "test", age: "18" }
     */
    private parseQueryString(queryStr: string): Record<string, string> {
        if (!queryStr) { return {}; }
        queryStr = queryStr.split('?').pop()!;
        if (!queryStr) { return {}; }

        queryStr = decodeURIComponent(queryStr);

        const arrQuery = queryStr.split('&'), oQuery: Record<string, string> = {};
        for (let i = 0, ii = arrQuery.length; i < ii; i++) {
            const arrTmp = arrQuery[i].split('=');
            if (arrTmp.length === 2) {
                oQuery[arrTmp[0]] = arrTmp[1];
            }
        }
        return oQuery;
    }

    /**
     * 发送网络请求
     * @param option 请求配置
     * @returns Promise对象，包含请求结果
     * @example
     * // 发送GET请求
     * app.net.request({
     *     url: "http://api.example.com/data",
     *     method: "get"
     * }).then(result => {
     *     if (!result.error) {
     *         console.log("请求成功:", result.response);
     *     }
     * });
     * 
     * // 发送POST请求
     * app.net.request({
     *     url: "http://api.example.com/submit",
     *     method: "post",
     *     data: { name: "test", age: 18 }
     * }).then(result => {
     *     if (!result.error) {
     *         console.log("提交成功:", result.response);
     *     }
     * });
     */
    public request<Response = any>(option: IOptions): Promise<{ error: string, response: Response, request: XMLHttpRequest | undefined }> {
        return new Promise((resolve) => {
            this._request(option, (error, response, request) => {
                if (error) {
                    Debug.error("NetManager", "请求<" + option.url + ">结果:", error);
                }
                resolve({ error, response, request });
            });
        });
    }
}