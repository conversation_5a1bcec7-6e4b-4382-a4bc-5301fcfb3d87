import { _decorator, AssetManager, AudioClip, AudioSource } from 'cc';
import { APP } from '../../core/APP';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 16:52
 * @filePath assets\core\manager\audio\AudioMusic.ts
 * @description 背景音乐
 * 1、播放一个新背景音乐时，先加载音乐资源，然后停止正在播放的背景资源同时施放当前背景音乐资源，最后播放新的背景音乐
 */
@ccclass('AudioMusic')
export class AudioMusic extends AudioSource {

    /** 背景音乐开关 */
    public switch: boolean = true;
    /** 背景音乐播放完成回调 */
    public onComplete: Function | null = null;
    /** 是否正在加载 */
    private _isLoading: boolean = false;
    /** 下一个加载的背景音乐资源包名 */
    private _nextBundleName: string | null = null;
    /** 下一个加载的背景音乐资源地址 */
    private _nextUrl: string | null = null;
    private _progress: number = 0;
    /** 获取音乐播放进度 */
    public get progress(): number {
        if (this.duration > 0)
            this._progress = this.currentTime / this.duration;
        return this._progress;
    }
    /**
     * 设置音乐当前播放进度
     * @param value     进度百分比0到1之间
     */
    public set progress(value: number) {
        this._progress = value;
        this.currentTime = value * this.duration;
    }

    protected start(): void {
        this.node.on(AudioSource.EventType.ENDED, this.onAudioEnded, this);
    }

    private onAudioEnded(): void {
        this.onComplete && this.onComplete();
    }

    /**
     * 加载音乐并播放
     * @param url          音乐资源地址
     * @param callback     加载完成回调
     * @param bundleName   资源包名
     */
    public async load(url: string, callback?: Function, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): Promise<void> {
        // 下一个加载的背景音乐资源
        if (this._isLoading) {
            this._nextBundleName = bundleName;
            this._nextUrl = url;
            return;
        }

        this._isLoading = true;
        const data: AudioClip = await APP.res.loadAsync(bundleName, url, AudioClip);
        if (data) {
            this._isLoading = false;

            // 处理等待加载的背景音乐
            if (this._nextUrl != null) {
                // 加载等待播放的背景音乐
                this.load(this._nextUrl, callback, this._nextBundleName!);
                this._nextBundleName = this._nextUrl = null;
            } else {
                callback && callback();

                // 正在播放的时候先关闭
                if (this.playing) {
                    this.stop();
                }

                // 删除当前正在播放的音乐
                this.release();

                // 播放背景音乐
                this.clip = data;
                this.play();
            }
        }
    }

    /** 释放当前背景音乐资源 */
    private release(): void {
        if (this.clip) {
            this.stop();
            this.clip.decRef();
            this.clip = null;
        }
    }

}