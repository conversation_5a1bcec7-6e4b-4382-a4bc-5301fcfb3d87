import { _decorator, CameraComponent, <PERSON><PERSON>, Component, director, EventTouch, Node, Vec2, Vec3 } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 游戏控制组件
 */
@ccclass('Play')
export class Play extends Component {
    @property(Canvas)
    private canvas: Canvas | null = null;

    @property(CameraComponent)
    private worldCamera: CameraComponent | null = null;

    @property(Node)
    private player: Node | null = null;

    @property
    private speed: number = 100;

    private targetPos: Vec3 = new Vec3();
    private moving: boolean = false;

    private static tempVec2: Vec2 = new Vec2();
    private static tempVec3: Vec3 = new Vec3();
    private static tempVec3_2: Vec3 = new Vec3();

    /**
     * 将触摸位置转换为节点空间坐标
     */
    private static convertTouchLocationToNode(
        touchPos: Vec2,
        node: Node,
        out: Vec3 | null = null,
        camera: CameraComponent | null = null
    ): Vec3 {
        const root = director.root;
        if (!root) {
            throw new Error('Director root is not available');
        }

        const renderCamera = camera || root.batcher2D.getFirstRenderCamera(node);
        if (!renderCamera) {
            throw new Error('No camera found');
        }

        const uiTransform = node._uiProps?.uiTransformComp;
        if (!uiTransform) {
            throw new Error('Node does not have UITransform component');
        }

        this.tempVec3.set(touchPos.x, touchPos.y, 0);
        renderCamera.screenToWorld(this.tempVec3, this.tempVec3);
        uiTransform.convertToNodeSpaceAR(this.tempVec3, this.tempVec3);

        if (!out) {
            out = new Vec3();
        }
        out.set(this.tempVec3);
        return out;
    }

    start(): void {
        if (!this.canvas) {
            console.error('Canvas is not set');
            return;
        }

        this.canvas.node.on(Node.EventType.TOUCH_START, (event: EventTouch) => {
            event.getLocation(Play.tempVec2);
            Play.convertTouchLocationToNode(
                Play.tempVec2,
                this.canvas!.node,
                Play.tempVec3,
                this.worldCamera
            );
            this.targetPos.set(Play.tempVec3);
            this.moving = true;
        });
    }

    update(deltaTime: number): void {
        if (!this.moving || !this.player) {
            return;
        }

        this.player.getPosition(Play.tempVec3);
        const currentPos = Play.tempVec3;
        const targetPos = Play.tempVec3_2.set(this.targetPos);
        const direction = targetPos.subtract(currentPos);

        if (direction.length() < 10) {
            this.moving = false;
            return;
        }

        direction.normalize();
        currentPos.add(direction.multiplyScalar(this.speed * deltaTime));
        this.player.setPosition(currentPos);
    }
} 