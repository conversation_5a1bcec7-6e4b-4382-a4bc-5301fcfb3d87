import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 数学工具类
 * @description 提供数学计算相关的工具方法
 * 包括数值限制、随机数生成、数字格式化等功能
 */
@ccclass('MathUtil')
export class MathUtil extends Component {

    /**
     * 限制数值在指定范围内
     * @description 将输入值限制在最小值和最大值之间
     * 如果值小于最小值，返回最小值；如果值大于最大值，返回最大值
     * @param value 要限制的值
     * @param minLimit 最小值
     * @param maxLimit 最大值
     * @returns 限制后的值
     * @example
     * MathUtil.Clamp(5, 0, 10) // 返回 5
     * MathUtil.Clamp(-1, 0, 10) // 返回 0
     * MathUtil.Clamp(11, 0, 10) // 返回 10
     */
    public static Clamp(value: number, minLimit: number, maxLimit: number): number {
        return value < minLimit ? minLimit : value > maxLimit ? maxLimit : value;
    }

    /**
     * 生成指定范围内的随机整数
     * @description 生成一个在最小值和最大值之间的随机整数（包含最小值和最大值）
     * @param min 最小值
     * @param max 最大值
     * @returns 随机整数
     * @example
     * MathUtil.RandomInt(1, 10) // 返回1到10之间的随机整数
     */
    public static RandomInt(min: number, max: number): number {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 生成指定范围内的随机浮点数
     * @description 生成一个在最小值和最大值之间的随机浮点数
     * @param min 最小值
     * @param max 最大值
     * @param decimal 保留的小数位数，默认为2位
     * @returns 随机浮点数
     * @example
     * MathUtil.RandomFloat(1, 10) // 返回1到10之间的随机浮点数，保留2位小数
     * MathUtil.RandomFloat(1, 10, 3) // 返回1到10之间的随机浮点数，保留3位小数
     */
    public static RandomFloat(min: number, max: number, decimal: number = 2): number {
        return parseFloat((Math.random() * (max - min) + min).toFixed(decimal));
    }

    /**
     * 将数字转换为中文单位计数
     * @description 将大数字转换为带中文单位的字符串表示
     * 支持万、亿、万亿等单位
     * @param value 要转换的数字
     * @param fixed 保留的小数位数，默认为2位
     * @returns 转换后的字符串
     * @example
     * MathUtil.NumberToTenThousand(12345) // 返回 "1.23万"
     * MathUtil.NumberToTenThousand(123456789) // 返回 "1.23亿"
     * MathUtil.NumberToTenThousand(1234) // 返回 "1234"
     */
    public static NumberToTenThousand(value: number, fixed: number = 2): string {
        const k = 10000;
        const sizes = ['', '万', '亿', '万亿'];
        if (value < k) {
            return value.toString();
        } else {
            const i = Math.floor(Math.log(value) / Math.log(k));
            return ((value / Math.pow(k, i))).toFixed(fixed) + sizes[i];
        }
    }
}