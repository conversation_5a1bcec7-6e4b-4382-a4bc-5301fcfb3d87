import { __private, _decorator, Camera, Canvas, Component, director, Game, game, Node, screen, sys, tween, UIOpacity, Widget } from 'cc';
import { AudioManager } from '../manager/audio/AudioManager';
import { EventManager } from '../manager/event/EventManager';
import { EventMessage } from '../manager/event/EventMessage';
import { GameManager } from '../manager/game/GameManager';
import { NetManager } from '../manager/network/NetManager';
import { StorageManager } from '../manager/storage/StorageManager';
import { TimerManager } from '../manager/timer/TimerManager';
import { UIConfig } from '../manager/ui/Defines';
import { UIManager } from '../manager/ui/UIManager';
import { APP } from './APP';
import { ResLoader } from './loader/ResLoader';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 11:47
 * @filePath assets\core\Root.ts
 * @description 框架根节点组件，负责初始化和管理框架的核心功能
 * 包括：场景管理、UI管理、事件系统、音频系统等
 */
@ccclass('Root')
export abstract class Root extends Component {

    @property
    private _gameNode: Node = null!;
    @property({ type: Node, tooltip: "游戏层 - 用于放置游戏主场景内容" })
    public get gameNode(): Node {
        if (!this._gameNode) {
            let node = this.findNodeFromName("game");
            if (!node) {
                node = new Node("game");
                director.getScene()?.addChild(node);
            }
            this._gameNode = node;
        }
        return this._gameNode;
    }
    public set gameNode(value: Node) {
        this._gameNode = value;
    }
    @property
    private _uiNode: Node = null!;
    @property({ type: Node, tooltip: "UI层 - 用于放置所有UI界面" })
    public get uiNode(): Node {
        if (!this._uiNode) {
            let node = this.findNodeFromName(Canvas);
            if (!node) {
                const canvas = new Node("Canvas");
                node = canvas.addComponent(Canvas);
                const widget = canvas.addComponent(Widget);
                widget.isAlignLeft = widget.isAlignRight = widget.isAlignTop = widget.isAlignBottom = true;
                widget.left = widget.right = widget.top = widget.bottom = 0;
                widget.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;
                director.getScene()?.addChild(canvas);
            }
            this._uiNode = node.node;
        }
        return this._uiNode;
    }
    public set uiNode(value: Node) {
        this._uiNode = value;
    }
    @property
    private _uiCamera: Camera = null!;
    @property({ type: Camera, tooltip: "UI相机 - 用于渲染UI层内容" })
    public get uiCamera(): Camera {
        if (!this._uiCamera) {
            this._uiCamera = this.findNodeFromName("UICamera", Camera)!;
        }
        return this._uiCamera;
    }
    public set uiCamera(value: Camera) {
        this._uiCamera = value;
    }
    @property
    private _gameCamera: Camera = null!;
    @property({ type: Camera, tooltip: "游戏相机 - 用于渲染游戏层内容" })
    public get gameCamera(): Camera {
        if (!this._gameCamera) {
            this._gameCamera = this.findNodeFromName("GameCamera", Camera)!;
        }
        return this._gameCamera;
    }
    public set gameCamera(value: Camera) {
        this._gameCamera = value;
    }
    // @property({ type: Prefab || Scene, tooltip: "初始打开场景或页面" })
    // private firstScene: Prefab | Scene = null!;

    /** 框架常驻节点 - 用于存放需要在场景切换时保持的节点 */
    private persist: Node = null!

    /** Logo节点 - 用于显示游戏启动时的Logo动画 */
    private logo: Node | null = null;
    /** Logo动画是否播放完成 */
    private endLogoPlay: boolean = false;
    /**
     * 游戏UI配置数据
     * @description 存储所有UI界面的配置信息
     * 使用UIID作为键，UIConfig作为值，定义每个界面的加载路径和资源管理方式
     * @example
// 页面UI标识枚举,用于定义游戏中所有UI界面的唯一标识.每个UI界面都应该在此枚举中定义对应的ID
export enum UIID {
    Start
}

    // 游戏UI配置数据,使用UIID作为键,UIConfig作为值,定义每个界面的加载路径和资源管理方式
export const GameUIConfigData: Record<UIID, UIConfig> = {
    [UIID.Start]: { prefab: "common/Start", bundle: BundleName.Game },
}
     */
    protected abstract gameUIConfigData: { [key: number]: UIConfig };

    // 函数重载声明
    private findNodeFromName(name: string): Node | undefined;
    private findNodeFromName<T extends Component>(type: __private.__types_globals__Constructor<T> | __private.__types_globals__AbstractedConstructor<T>): T | undefined;
    private findNodeFromName<T extends Component>(name: string, type: __private.__types_globals__Constructor<T> | __private.__types_globals__AbstractedConstructor<T>): T | undefined;
    // 实现
    private findNodeFromName<T extends Component>(nameOrType: string | __private.__types_globals__Constructor<T> | __private.__types_globals__AbstractedConstructor<T>, type?: __private.__types_globals__Constructor<T> | __private.__types_globals__AbstractedConstructor<T>): Node | T | undefined {
        if (type) {
            return this.traverseNodes(director.getScene()!, nameOrType as string, type);
        }
        if (typeof nameOrType === 'string') {
            return this.traverseNodes(director.getScene()!, nameOrType);
        }
        return this.traverseNodes(director.getScene()!, nameOrType as __private.__types_globals__Constructor<T>);
    }

    private traverseNodes(node: Node, name: string): Node | undefined;
    private traverseNodes<T extends Component>(node: Node, type: __private.__types_globals__Constructor<T> | __private.__types_globals__AbstractedConstructor<T>): T | undefined;
    private traverseNodes<T extends Component>(node: Node, name: string, type: __private.__types_globals__Constructor<T> | __private.__types_globals__AbstractedConstructor<T>): T | undefined;
    private traverseNodes<T extends Component>(node: Node, nameOrType: string | __private.__types_globals__Constructor<T> | __private.__types_globals__AbstractedConstructor<T>, type?: __private.__types_globals__Constructor<T> | __private.__types_globals__AbstractedConstructor<T>): Node | T | undefined {
        // 处理当前节点
        if (type) {
            // 同时检查名称和类型
            if (node.name === nameOrType) {
                const component = node.getComponent(type);
                if (component) {
                    return component;
                }
            }
        } else if (typeof nameOrType === 'string') {
            // 只检查名称
            if (node.name === nameOrType) {
                return node;
            }
        } else {
            // 只检查类型
            const component = node.getComponent(nameOrType as __private.__types_globals__Constructor<T>);
            if (component) {
                return component;
            }
        }

        // 遍历所有子节点
        for (const child of node.children) {
            const result = type
                ? this.traverseNodes(child, nameOrType as string, type)
                : typeof nameOrType === 'string'
                    ? this.traverseNodes(child, nameOrType)
                    : this.traverseNodes(child, nameOrType as __private.__types_globals__Constructor<T>);
            if (result) {
                return result;
            }
        }
        return undefined;
    }

    /**
     * 组件加载时调用
     * 初始化框架核心功能
     */
    protected onLoad(): void {
        this.startLogo();
        // 创建常驻节点
        this.persist = new Node("FrameworkPersistNode");
        director.addPersistRootNode(this.persist);
        // 初始化
        this.initBuiltManager();
        this.registerGameEvent();
        this.checkLogoComplete();
    }

    /**
     * 检查Logo动画是否播放完成
     * 如果完成则开始游戏，否则继续等待
     */
    private checkLogoComplete(): void {
        if (null == this.logo) {
            // 直接开始
            this.startGame();
            return;
        }
        const timeId = APP.timer.registerLoop(this, () => {
            if (this.endLogoPlay) {
                this.logo?.destroy();
                APP.timer.unRegister(timeId);
                this.startGame();
            }
        });
    }

    /**
     * 开始游戏
     * 打开初始UI界面
     */
    private startGame(): void {
        // 开始游戏
        APP.ui.open(0);
    }

    /**
     * 注册游戏全局事件
     * 包括：游戏显示/隐藏、窗口大小改变、全屏切换、屏幕方向改变等
     */
    private registerGameEvent(): void {
        // 游戏显示事件
        game.on(Game.EVENT_SHOW, this.onShow, this);
        // 游戏隐藏事件
        game.on(Game.EVENT_HIDE, this.onHide, this);
        // 游戏尺寸修改事件
        if (!sys.isMobile) {
            screen.on("window-resize", () => {
                APP.event.dispatchEvent(EventMessage.GAME_RESIZE);
            }, this);

            screen.on("fullscreen-change", () => {
                APP.event.dispatchEvent(EventMessage.GAME_FULL_SCREEN);
            }, this);
        }
        screen.on("orientation-change", () => {
            APP.event.dispatchEvent(EventMessage.GAME_ORIENTATION);
        }, this);
    }

    /**
     * 游戏隐藏事件处理
     * 暂停游戏逻辑、音频、渲染等
     */
    private onHide(): void {
        APP.timer.save();             // 处理切到后台后记录切出时间
        APP.audio.pauseAll();         // 暂停所有音乐播放
        director.pause();              // 暂停正在运行的场景，该暂停只会停止游戏逻辑执行，但是不会停止渲染和 UI 响应
        game.pause();                  // 暂停游戏主循环。包含：游戏逻辑、渲染、输入事件派发（Web 和小游戏平台除外）
        APP.event.dispatchEvent(EventMessage.GAME_HIDE);
    }

    /**
     * 游戏显示事件处理
     * 恢复游戏逻辑、音频、渲染等
     */
    private onShow(): void {
        APP.timer.load();              // 处理回到游戏时减去逝去时间
        APP.audio.resumeAll();         // 恢复所有暂停的音乐播放
        director.resume();              // 恢复暂停场景的游戏逻辑，如果当前场景没有暂停将没任何事情发生
        game.resume();                  // 恢复游戏主循环。包含：游戏逻辑，渲染，事件处理，背景音乐和所有音效
        APP.event.dispatchEvent(EventMessage.GAME_SHOW);
    }

    /**
     * 初始化所有内置管理器
     * 按照依赖顺序初始化各个管理器
     */
    private initBuiltManager(): void {
        // 存档管理器要在其他管理器之前初始化
        APP.storage = new Node("StorageManager").addComponent(StorageManager);
        APP.net = new NetManager();
        APP.res = new ResLoader();
        APP.ui = new Node("UIManager").addComponent(UIManager);
        // @ts-ignore
        APP.ui.initLayer(this.uiNode, this.uiCamera, this.gameUIConfigData);
        APP.timer = new Node("TimerManager").addComponent(TimerManager);
        APP.event = new Node("EventManager").addComponent(EventManager);
        APP.audio = new Node("AudioManager").addComponent(AudioManager);
        APP.game = new GameManager(this.gameNode, this.gameCamera);
        // @ts-ignore
        APP.setManagerForPersist();
    }

    /**
     * 启动Logo动画
     * 播放淡入淡出效果
     */
    private startLogo(): void {
        this.logo = this.uiNode.getChildByName("Logo");
        if (null == this.logo) return;
        const opacity = this.logo.children[0]?.getComponent(UIOpacity);
        if (null == opacity) return;
        opacity.opacity = 0;
        tween(opacity).to(0.5, { opacity: 255 }).delay(1).to(0.5, { opacity: 0 }).call(() => this.endLogoPlay = true).start();
    }
}