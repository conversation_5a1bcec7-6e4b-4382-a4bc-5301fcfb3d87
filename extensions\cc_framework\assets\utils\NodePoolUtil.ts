import { __private, instantiate, js, Node, NodePool, Prefab } from "cc";
import { Debug } from "../core/logger/Debug";

type PoolType = __private._extensions_ccpool_node_pool__Constructor<__private._extensions_ccpool_node_pool__IPoolHandlerComponent> | string;

/**
 * 节点池工具类
 * @description 提供节点对象池的管理功能
 * 用于优化频繁创建和销毁的节点，提高性能
 * 支持自定义节点处理器，实现节点的复用和重置
 */
export class NodePoolUtil {
    /** 类名，用于日志输出 */
    private static jsName: string = js.getClassName(this);

    /** 对象池集合，key为池名称，value为池配置信息 */
    private static pool: Record<string, { sourceNode: Node | Prefab, pool: NodePool, type: PoolType | undefined }> = {};

    /** 
     * 创建对象池
     * @description 创建一个新的节点对象池
     * @param poolName 对象池名称
     * @param prefab 预制体或节点，作为对象池的模板
     * @param poolHandlerComp 可选的节点处理器组件类型
     */
    public static Create(poolName: string, prefab: Prefab | Node, poolHandlerComp?: PoolType): void {
        if (this.pool[poolName]) {
            Debug.warn(`${this.jsName} 对象池 ${poolName} 已存在`);
            return;
        }
        this.pool[poolName] = {
            sourceNode: prefab,
            pool: new NodePool(poolHandlerComp),
            type: poolHandlerComp
        }
    }

    /** 
     * 从对象池获取节点
     * @description 从指定对象池中获取一个节点，如果池为空则创建新节点
     * @param poolName 对象池名称
     * @param args 传递给节点处理器的参数
     * @returns 获取的节点
     */
    public static Get(poolName: string, ...args: any[]): Node {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return null!;
        }
        let node: Node = null!;
        if (this.pool[poolName].pool.size() > 0) {
            node = this.pool[poolName].pool.get(...args)!;
        } else {
            if (this.pool[poolName].sourceNode instanceof Prefab) {
                node = instantiate(this.pool[poolName].sourceNode);
            } else {
                node = instantiate(this.pool[poolName].sourceNode);
            }
            // @ts-ignore
            const handler = this.pool[poolName].type ? node.getComponent(this.pool[poolName].type) : null;
            if (handler && handler.reuse) {
                handler.reuse(args);
            }
        }
        return node;
    }

    /** 
     * 回收节点到对象池
     * @description 将节点或节点数组回收到指定对象池
     * @param poolName 对象池名称
     * @param node 要回收的节点或节点数组
     */
    public static Put(poolName: string, node: Node | Array<Node>): void {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return;
        }
        if (Array.isArray(node)) {
            for (const item of node.slice()) {
                if (!item) continue;
                this.pool[poolName].pool.put(item);
            }
        } else {
            if (!node) return;
            this.pool[poolName].pool.put(node);
        }
    }

    /** 
     * 清空对象池
     * @description 销毁对象池中所有缓存的节点
     * @param poolName 对象池名称
     */
    public static Clear(poolName: string): void {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return;
        }
        this.pool[poolName].pool.clear();
    }

    /** 
     * 销毁对象池
     * @description 清空并销毁指定的对象池
     * @param poolName 对象池名称
     */
    public static Destroy(poolName: string): void {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return;
        }
        this.Clear(poolName);
        delete this.pool[poolName];
    }

    /** 
     * 获取对象池大小
     * @description 获取指定对象池中当前可用的节点数量
     * @param poolName 对象池名称
     * @returns 对象池中可用的节点数量
     */
    public static Size(poolName: string): number {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return 0;
        }
        return this.pool[poolName].pool.size();
    }

    /** 
     * 判断对象池是否存在
     * @description 检查指定对象池是否已创建
     * @param poolName 对象池名称
     * @returns 对象池是否存在
     */
    public static HasPool(poolName: string): boolean {
        return this.pool[poolName] !== undefined;
    }
}