import { Entity } from "db://framework/ECS/Entity";
import { EntitySystem } from "db://framework/ECS/systems/EntitySystem";
import { Matcher } from "db://framework/ECS/utils/Matcher";
import { Time } from "db://framework/utils/Time";
import { Movement } from "../components/Movement";
import { Transform } from "../components/Transform";

/**
 * <AUTHOR>
 * @data 2025-08-04 10:04
 * @filePath assets\lawnMowerDemo_ECS\scripts\systems\MovementSystem.ts
 * @description 
 */
export class MovementSystem extends EntitySystem {
    /** 移动阈值 */
    private readonly MOVEMENT_THRESHOLD = 0.1;
    /** 旋转阈值 */
    private readonly ROTATION_THRESHOLD = 0.001;

    constructor() {
        super(Matcher.empty().all(Transform, Movement));
    }

    protected process(entities: Entity[]): void {
        const deltaTime = Time.deltaTime;
        entities.forEach(entity => { this.processEntityMovement(entity, deltaTime); });
    }

    private processEntityMovement(entity: Entity, deltaTime: number): void {
        const transform = entity.getComponent(Transform);
        const movement = entity.getComponent(Movement);

        if (!transform || !movement) return;

        const inputDirLen = movement.inputDirection.x * movement.inputDirection.x +
            movement.inputDirection.y * movement.inputDirection.y;

        if (inputDirLen > this.MOVEMENT_THRESHOLD * this.MOVEMENT_THRESHOLD) {
            const invLen = 1 / Math.sqrt(inputDirLen);
            const normalizedX = movement.inputDirection.x * invLen;
            const normalizedY = movement.inputDirection.y * invLen;

            const moveDistance = movement.maxSpeed * deltaTime;

            transform.previousPosition.set(transform.position);
            transform.position.x += normalizedX * moveDistance;
            transform.position.y += normalizedY * moveDistance;

            movement.velocity.set(normalizedX * movement.maxSpeed, normalizedY * movement.maxSpeed);

            const velocityLen = movement.velocity.x * movement.velocity.x + movement.velocity.y * movement.velocity.y;
            if (velocityLen > 100) {
                const targetRotation = Math.atan2(movement.velocity.y, movement.velocity.x);

                let angleDiff = targetRotation - transform.rotation;
                if (angleDiff > Math.PI) angleDiff -= Math.PI * 2;
                else if (angleDiff < -Math.PI) angleDiff += Math.PI * 2;

                if (Math.abs(angleDiff) > this.ROTATION_THRESHOLD) {
                    transform.rotation += angleDiff * 8 * deltaTime;
                }
            }
        } else {
            movement.velocity.set(0, 0);
        }
    }

}