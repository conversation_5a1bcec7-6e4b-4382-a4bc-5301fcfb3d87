import { Component } from "db://framework/ECS/Component";

/**
 * <AUTHOR>
 * @data 2025-08-04 10:41
 * @filePath assets\lawnMowerDemo_ECS\scripts\components\Weapon.ts
 * @description 
 */
export class Weapon extends Component {
    private _damage: number = 10;
    private _fireRate: number = 2
    private _fireTimer: number = 0;
    private _autoFire: boolean = true;
    public get autoFire(): boolean { return this._autoFire; }
    // public set autoFire(value: boolean) { this._autoFire = value; }
    constructor(damage: number = 10, fireRate: number = 2) {
        super();
        this._damage = damage;
        this._fireRate = fireRate;
    }
    public updateTimer(deltaTime: number): void {
        if (this._fireTimer > 0) {
            this._fireTimer -= deltaTime;
        }
    }
    /** 检查是否可以开火 */
    public canFire(): boolean {
        return this._fireTimer <= 0;
    }

}