import { _decorator, sys } from "cc";
import { BaseManager } from "../../base/BaseManager";
const { ccclass, property } = _decorator;

const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
/**
 * <AUTHOR>
 * @data 2024-07-05 17:29
 * @filePath assets\scripts\tools\manager\StorageManager.ts
 * @description 存储管理器 - 负责游戏数据的本地存储和读取
 * 提供数据的加密存储、缓存管理、自动同步等功能
 */
@ccclass('StorageManager')
export class StorageManager extends BaseManager {
    /** 数据缓存 - 用于存储当前会话中的数据，减少IO操作 */
    private dataCache: Record<string, any> = {};
    /** localStorage缓存 - 用于记录已存储的数据，避免重复写入 */
    private localStorageCache: Record<string, any> = {};

    /**
     * 加密密钥
     * - 如果需要加密内容，请设置密钥的值
     * - 建议使用随机生成的字符串作为密钥
     */
    private secretKey: string = '';

    /**
     * 组件加载时调用
     * 初始化存储管理器
     */
    protected onLoad(): void {
        this.log("存储管理器初始化完成");
    }

    /**
     * 获取存档数据，如果存档不存在则返回默认值
     * @param key 存档键名
     * @param defaultValue 默认值（可选）
     * @returns 存档值
     * @example
     * // 获取用户数据，如果不存在则返回默认值
     * const userData = app.storage.getItem("userData", { level: 1, score: 0 });
     */
    public getItem<T>(key: string, defaultValue?: T): T {
        const encryptedKey = this.encode(key, this.secretKey);
        if (!this.dataCache[key]) {
            let data = sys.localStorage.getItem(encryptedKey);
            data = this.decode(data, this.secretKey);
            if (!data || data == "undefined" || data == "null") {
                this.removeItem(key);
            }
            this.dataCache[key] = data ? JSON.parse(data) : defaultValue;
            if (!this.dataCache[key]) this.removeItem(key);
        }
        return this.dataCache[key];
    }

    /**
     * 解密数据
     * @param encryptedText 加密的文本
     * @param key 解密密钥
     * @returns 解密后的文本
     */
    private decode(encryptedText: string | null, key: string): string | null {
        if (!encryptedText) return null;
        key = key || chars;
        let decrypted = '';
        for (let i = 0; i < encryptedText.length; i++) {
            const charCode = encryptedText.charCodeAt(i) ^ key.charCodeAt(i % key.length);
            decrypted += String.fromCharCode(charCode);
        }
        return decrypted;
    }

    /**
     * 设置存档数据
     * @param key 存档键名
     * @param value 要存储的值
     * @example
     * // 保存用户数据
     * app.storage.setItem("userData", { level: 2, score: 100 });
     */
    public setItem(key: string, value: unknown): void {
        this.dataCache[key] = value;
        const encryptedKey = this.encode(key, this.secretKey);
        const encryptedData = this.encode(JSON.stringify(value), this.secretKey);
        // 如果当前localStorage中的值和新的值相同则不进行设置减少IO消耗
        if (this.localStorageCache[encryptedKey] && this.localStorageCache[encryptedKey] == encryptedData) return;
        this.localStorageCache[encryptedKey] = encryptedData;
        sys.localStorage.setItem(encryptedKey, encryptedData);
    }

    /**
     * 加密数据
     * @param text 要加密的文本
     * @param key 加密密钥
     * @returns 加密后的文本
     */
    private encode(text: string, key: string): string {
        key = key || chars;
        let encrypted = '';
        for (let i = 0; i < text.length; i++) {
            const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
            encrypted += String.fromCharCode(charCode);
        }
        return encrypted;
    }

    /**
     * 删除指定存档
     * @param key 要删除的存档键名
     * @example
     * // 删除用户数据
     * app.storage.removeItem("userData");
     */
    public removeItem(key: string): void {
        if (this.dataCache[key]) delete this.dataCache[key];
        const encryptedKey = this.encode(key, this.secretKey);
        if (this.localStorageCache[encryptedKey]) delete this.localStorageCache[encryptedKey];
        sys.localStorage.removeItem(encryptedKey);
    }

    /**
     * 清除所有本地存档
     * 注意：此操作不可恢复，请谨慎使用
     * @example
     * // 清除所有存档
     * app.storage.clear();
     */
    public clear(): void {
        this.dataCache = {};
        this.localStorageCache = {};
        sys.localStorage.clear();
    }
}