/**
 * <AUTHOR>
 * @data 2025-07-16 22:51
 * @filePath extensions\cc_framework\assets\utils\ObjectUtil.ts
 * @description 
 */
export class ObjectUtil {

    /**
     * 深拷贝一个对象
     * 建议方法名：deepClone
     * @param source 要拷贝的对象
     * @returns 拷贝后的新对象
     */
    public static deepClone(source: any): any {
        const weakMap = new WeakMap();

        const clone = (value: any): any => {
            if (value === null) return null;

            const constructor = value.constructor;
            switch (constructor) {
                case Array:
                    return (function (arr: any[]) {
                        const result: any[] = [];
                        weakMap.set(arr, result);
                        arr.forEach(item => result.push(deepClone(item)));
                        return result;
                    })(value);
                case Map:
                    return (function (map: Map<any, any>) {
                        const result = new Map();
                        weakMap.set(map, result);
                        map.forEach((val, key) => result.set(key, deepClone(val)));
                        return result;
                    })(value);
                case Set:
                    return (function (set: Set<any>) {
                        const result = new Set();
                        weakMap.set(set, result);
                        set.forEach(item => result.add(deepClone(item)));
                        return result;
                    })(value);
                case Date:
                case Number:
                case String:
                    return new constructor(value);
                case Boolean:
                    return new constructor(value.valueOf());
                case RegExp:
                    return (function (regex: RegExp) {
                        const result = new RegExp(regex.source, regex.flags);
                        result.lastIndex = regex.lastIndex;
                        return result;
                    })(value);
                default:
                    break;
            }

            const proto = Object.create(Object.getPrototypeOf(value));
            weakMap.set(value, proto);
            Reflect.ownKeys(value).forEach(key => {
                const descriptor = Reflect.getOwnPropertyDescriptor(value, key);
                if (descriptor) {
                    const newDescriptor = { ...descriptor };
                    if ('value' in descriptor) {
                        newDescriptor.value = deepClone(value[key]);
                    }
                    Reflect.defineProperty(proto, key, newDescriptor);
                }
            });
            return proto;
        };

        const deepClone = (obj: any): any => {
            if (typeof obj !== 'object' || obj === null) {
                return obj;
            } else {
                return weakMap.has(obj) ? weakMap.get(obj) : clone(obj);
            }
        };

        return deepClone(source);
    }

}