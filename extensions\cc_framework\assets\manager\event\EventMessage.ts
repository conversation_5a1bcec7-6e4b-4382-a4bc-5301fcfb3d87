/**
 * <AUTHOR>
 * @data 2025-03-13 16:12
 * @filePath assets\core\manager\event\EventMessage.ts
 * @description 事件消息定义 - 包含全局事件监听器类型和系统事件枚举
 * 定义了游戏框架内部使用的全局事件类型和事件监听器接口
 */

/**
 * 全局事件监听器函数类型
 * @param event 事件名称 - 用于标识事件的唯一字符串
 * @param args 事件参数 - 可变参数列表，用于传递事件相关的数据
 * @description 用于定义事件监听器的函数签名，支持任意数量的事件参数
 */
export type ListenerFunc = (event: string, ...args: any) => void;

/** 
 * 框架内部全局事件枚举
 * @description 定义了游戏框架内部使用的系统级事件类型
 * 这些事件由框架自动触发，用于处理游戏生命周期和系统状态变化
 */
export enum EventMessage {
    /** 游戏从后台进入前台事件 - 当游戏从后台恢复时触发 */
    GAME_SHOW = "GAME_ENTER",

    /** 游戏切到后台事件 - 当游戏进入后台时触发 */
    GAME_HIDE = "GAME_EXIT",

    /** 游戏画笔尺寸变化事件 - 当游戏窗口大小改变时触发 */
    GAME_RESIZE = "GAME_RESIZE",

    /** 游戏全屏事件 - 当游戏切换全屏状态时触发 */
    GAME_FULL_SCREEN = "GAME_FULL_SCREEN",

    /** 游戏旋转屏幕事件 - 当游戏屏幕方向改变时触发 */
    GAME_ORIENTATION = "GAME_ORIENTATION"
}
