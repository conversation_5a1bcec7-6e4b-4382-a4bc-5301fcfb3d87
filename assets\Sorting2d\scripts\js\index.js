System.register("chunks:///_virtual/Benchmark.ts", ["./rollupPluginModLoBabelHelpers.js", "cc"], (function (t) {
    var e, r, n, i, o, a, c, u, l, s;
    return {
        setters: [function (t) {
            e = t.applyDecoratedDescriptor,
                r = t.inherits<PERSON>oose,
                n = t.initializerDefineProperty,
                i = t.assertThisInitialized
        }
            , function (t) {
                o = t.cclegacy,
                    a = t._decorator,
                    c = t.Node,
                    u = t.profiler,
                    l = t.instantiate,
                    s = t.Component
            }
        ],
        execute: function () {
            var p, f, h, d, g, y;
            o._RF.push({}, "182df9I39dMUr8gZbZhKEkR", "Benchmark", void 0);
            var b = a.ccclass
                , m = a.property;
            t("Benchmark", (p = b("Benchmark"),
                f = m(c),
                p((g = e((d = function (t) {
                    function e() {
                        for (var e, r = arguments.length, o = new Array(r), a = 0; a < r; a++)
                            o[a] = arguments[a];
                        return e = t.call.apply(t, [this].concat(o)) || this,
                            n(e, "target", g, i(e)),
                            n(e, "count", y, i(e)),
                            e
                    }
                    r(e, t);
                    var o = e.prototype;
                    return o.start = function () {
                        u.showStats();
                        for (var t = 0; t < this.count; t++) {
                            l(this.target).parent = this.node
                        }
                    }
                        ,
                        o.update = function (t) { }
                        ,
                        e
                }(s)).prototype, "target", [f], {
                    configurable: !0,
                    enumerable: !0,
                    writable: !0,
                    initializer: function () {
                        return null
                    }
                }),
                    y = e(d.prototype, "count", [m], {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                        initializer: function () {
                            return 0
                        }
                    }),
                    h = d)) || h));
            o._RF.pop()
        }
    }
}
));

System.register("chunks:///_virtual/ccc_ext.ts", ["cc"], (function () {
    var e, t;
    return {
        setters: [function (i) {
            e = i.cclegacy,
                t = i.Node
        }
        ],
        execute: function () {
            e._RF.push({}, "f6950K+rBtM9JtC10qZw5ps", "ccc_ext", void 0),
                Object.defineProperty(t.prototype, "_visible", {
                    value: !0,
                    writable: !0,
                    enumerable: !1,
                    configurable: !0
                }),
                Object.defineProperty(t.prototype, "visible", {
                    get: function () {
                        return this._visible
                    },
                    set: function (e) {
                        this._visible = e
                    },
                    enumerable: !0,
                    configurable: !0
                }),
                e._RF.pop()
        }
    }
}
));

System.register("chunks:///_virtual/ccc_renderer_patch.ts", ["cc", "./render-graph.ts", "./sorting-2d.ts"], (function (t) {
    var e, r, a, i, s, n, o, c, h;
    return {
        setters: [function (t) {
            e = t.cclegacy,
                r = t.gfx,
                a = t.clamp,
                i = t.UI,
                s = t.math,
                n = t.EPSILON,
                o = t.StencilManager
        }
            , function (t) {
                c = t.SortingManager
            }
            , function (t) {
                h = t.DEFAULT_SORTING2D
            }
        ],
        execute: function () {
            function p(t, e) {
                for (var i, s, n, o = t.vertexFormat, c = t.chunk.vb, h = 0, p = 0; p < o.length; ++p) {
                    if (i = o[p],
                        (s = r.FormatInfos[i.format]).hasAlpha)
                        if (n = t.floatStride,
                            s.size / s.count == 1)
                            for (var l = ~~a(Math.round(255 * e), 0, 255), E = h; E < c.length; E += n)
                                c[E] = (4294967040 & c[E] | l) >>> 0;
                        else if (s.size / s.count == 4)
                            for (var d = h + 3; d < c.length; d += n)
                                c[d] = e;
                    h += s.size >> 2
                }
            }
            t("updateOpacity", p),
                e._RF.push({}, "1788c10gD9N75FH0AE002iL", "ccc_renderer_patch", void 0);
            var l = t("Stage", function (t) {
                return t[t.DISABLED = 0] = "DISABLED",
                    t[t.CLEAR = 1] = "CLEAR",
                    t[t.ENTER_LEVEL = 2] = "ENTER_LEVEL",
                    t[t.ENABLED = 3] = "ENABLED",
                    t[t.EXIT_LEVEL = 4] = "EXIT_LEVEL",
                    t[t.CLEAR_INVERTED = 5] = "CLEAR_INVERTED",
                    t[t.ENTER_LEVEL_INVERTED = 6] = "ENTER_LEVEL_INVERTED",
                    t
            }({}));
            i.prototype.update = function () {
                for (var t = this._screens, e = 0, r = 0; r < t.length; ++r) {
                    var a = t[r]
                        , i = a._getRenderScene();
                    if (a.enabledInHierarchy && i) {
                        this._opacityDirty = 0,
                            this._pOpacity = 1;
                        var s = a.sorting2DSettings;
                        s && s.enabled ? (c.renderRoot = a,
                            c.camera = a.cameraComponent,
                            c.settings = s,
                            c.center = a.node.worldPosition,
                            this.collect(a.node),
                            c.traverse(this.renderEntity, this),
                            s.clearDirty()) : this.walk(a.node),
                            this.autoMergeBatches(this._currComponent),
                            this.resetRenderStates();
                        var n = 0;
                        if (this._batches.length > e)
                            for (; e < this._batches.length; ++e) {
                                var o = this._batches.array[e];
                                if (o.model)
                                    for (var h = o.model.subModels, p = 0; p < h.length; p++)
                                        h[p].priority = n++;
                                else
                                    o.descriptorSet = this._descriptorSetCache.getDescriptorSet(o);
                                i.addBatch(o)
                            }
                    }
                }
            }
                ,
                i.prototype.collect = function (t, e) {
                    if (void 0 === e && (e = 0),
                        t.activeInHierarchy && t.visible) {
                        var r = t.children
                            , a = t._uiProps
                            , i = a.uiComp
                            , o = this._pOpacity
                            , p = o
                            , E = i && i.color ? i.color.a / 255 : 1;
                        if (this._pOpacity = p *= E * a.localOpacity,
                            a.setOpacity(p),
                            i && (i.sorting2D || (i.sorting2D = h.clone(),
                                i.useDefaultSort = !0),
                                i.enabledInHierarchy && (i.enterMask = i.stencilStage === l.ENTER_LEVEL || i.stencilStage === l.ENTER_LEVEL_INVERTED)),
                            !s.approx(p, 0, n)) {
                            var d;
                            if (a.colorDirty && this._opacityDirty++,
                                i && i.enabledInHierarchy ? (d = c.addEntity(i, e),
                                    this._opacityDirty && !i.useVertexOpacity && i.renderData && i.renderData.vertexCount > 0 && (d.opacity = p)) : d = c.addEntity(i, e),
                                r.length > 0 && !t._static)
                                for (var y = 0; y < r.length; ++y) {
                                    var _ = r[y];
                                    this.collect(_, e + 1)
                                }
                            a.colorDirty && (this._opacityDirty--,
                                a.colorDirty = !1)
                        }
                        this._pOpacity = o
                    }
                }
                ,
                i.prototype.renderEntity = function (t) {
                    var e = t.renderer.node._uiProps.uiComp;
                    if (e && e.enabledInHierarchy) {
                        var r = t.opacity;
                        if (e.fillBuffers(this),
                            r > 0 && e && !e.useVertexOpacity && e.renderData && e.renderData.vertexCount > 0) {
                            p(e.renderData, r);
                            var a = e.renderData.getMeshBuffer();
                            a && a.setDirty()
                        }
                        if (e.enterMask || e.postUpdateAssembler(this),
                            t.exitMask && o.sharedManager.getMaskStackSize() > 0)
                            c.popMask().postUpdateAssembler(this),
                                this.autoMergeBatches(this._currComponent),
                                this.resetRenderStates(),
                                o.sharedManager.exitMask()
                    }
                }
                ,
                i.prototype.walk = function (t, e) {
                    if (void 0 === e && (e = 0),
                        t.activeInHierarchy && t.visible) {
                        var r = t.children
                            , a = t._uiProps
                            , i = a.uiComp
                            , c = this._pOpacity
                            , h = c
                            , E = i && i.color ? i.color.a / 255 : 1;
                        if (this._pOpacity = h *= E * a.localOpacity,
                            a.setOpacity(h),
                            !s.approx(h, 0, n)) {
                            if (a.colorDirty && this._opacityDirty++,
                                i && i.enabledInHierarchy && i.fillBuffers(this),
                                this._opacityDirty && i && !i.useVertexOpacity && i.renderData && i.renderData.vertexCount > 0) {
                                p(i.renderData, h);
                                var d = i.renderData.getMeshBuffer();
                                d && d.setDirty()
                            }
                            if (r.length > 0 && !t._static)
                                for (var y = 0; y < r.length; ++y) {
                                    var _ = r[y];
                                    this.walk(_, e)
                                }
                            a.colorDirty && (this._opacityDirty--,
                                a.colorDirty = !1)
                        }
                        this._pOpacity = c,
                            i && i.enabledInHierarchy && (i.postUpdateAssembler(this),
                                (i.stencilStage === l.ENTER_LEVEL || i.stencilStage === l.ENTER_LEVEL_INVERTED) && o.sharedManager.getMaskStackSize() > 0 && (this.autoMergeBatches(this._currComponent),
                                    this.resetRenderStates(),
                                    o.sharedManager.exitMask())),
                            e += 1
                    }
                }
                ,
                e._RF.pop()
        }
    }
}
));



System.register("chunks:///_virtual/CustomSorts.ts", ["cc", "./sorts.ts"], (function (t) {
    var r, o, s, a, n;
    return {
        setters: [function (t) {
            r = t.cclegacy,
                o = t.v3,
                s = t.v2
        }
            , function (t) {
                a = t.SortFactory,
                    n = t.EnumSort2DMode
            }
        ],
        execute: function () {
            r._RF.push({}, "f492augQRpAW53RDDsXdHPg", "CustomSorts", void 0);
            var i = o()
                , e = t("GridSort", function () {
                    function t() {
                        this.gridSize = s(10, 10)
                    }
                    var r = t.prototype;
                    return r.ready = function (t, r) {
                        if (r.settings && (t.node.hasChangedFlags || !t.sortData || r.settings.dirty)) {
                            null == t.sortData && (t.sortData = {
                                x: 0,
                                y: 0
                            });
                            var o = t.node.getWorldPosition(i);
                            o.subtract(r.center),
                                t.sortData.x = Math.floor(o.x / this.gridSize.x),
                                t.sortData.y = Math.floor(o.y / this.gridSize.y)
                        }
                    }
                        ,
                        r.compare = function (t, r) {
                            if (null == t.sortData)
                                return 0;
                            var o = t.sortData.x
                                , s = t.sortData.y
                                , a = r.sortData.x
                                , n = r.sortData.y;
                            return s == n ? o - a : n - s
                        }
                        ,
                        t
                }());
            a.addCustomSort("GridSort", n.CustomAxis + 1, new e),
                r._RF.pop()
        }
    }
}
));

System.register("chunks:///_virtual/debug-view-runtime-control.ts", ["./rollupPluginModLoBabelHelpers.js", "cc"], (function (t) {
    var e, o, i, n, s, l, r, a, g, h, p, c, C, d, m, u, L;
    return {
        setters: [function (t) {
            e = t.applyDecoratedDescriptor,
                o = t.inheritsLoose,
                i = t.initializerDefineProperty,
                n = t.assertThisInitialized
        }
            , function (t) {
                s = t.cclegacy,
                    l = t._decorator,
                    r = t.Node,
                    a = t.Color,
                    g = t.Canvas,
                    h = t.UITransform,
                    p = t.instantiate,
                    c = t.Label,
                    C = t.RichText,
                    d = t.Toggle,
                    m = t.Button,
                    u = t.director,
                    L = t.Component
            }
        ],
        execute: function () {
            var f, M, b, v, T, S, x, E, I;
            s._RF.push({}, "b2bd1+njXxJxaFY3ymm06WU", "debug-view-runtime-control", void 0);
            var A = l.ccclass
                , y = l.property;
            t("DebugViewRuntimeControl", (f = A("internal.DebugViewRuntimeControl"),
                M = y(r),
                b = y(r),
                v = y(r),
                f((x = e((S = function (t) {
                    function e() {
                        for (var e, o = arguments.length, s = new Array(o), l = 0; l < o; l++)
                            s[l] = arguments[l];
                        return e = t.call.apply(t, [this].concat(s)) || this,
                            i(e, "compositeModeToggle", x, n(e)),
                            i(e, "singleModeToggle", E, n(e)),
                            i(e, "EnableAllCompositeModeButton", I, n(e)),
                            e._single = 0,
                            e.strSingle = ["No Single Debug", "Vertex Color", "Vertex Normal", "Vertex Tangent", "World Position", "Vertex Mirror", "Face Side", "UV0", "UV1", "UV Lightmap", "Project Depth", "Linear Depth", "Fragment Normal", "Fragment Tangent", "Fragment Binormal", "Base Color", "Diffuse Color", "Specular Color", "Transparency", "Metallic", "Roughness", "Specular Intensity", "IOR", "Direct Diffuse", "Direct Specular", "Direct All", "Env Diffuse", "Env Specular", "Env All", "Emissive", "Light Map", "Shadow", "AO", "Fresnel", "Direct Transmit Diffuse", "Direct Transmit Specular", "Env Transmit Diffuse", "Env Transmit Specular", "Transmit All", "Direct Internal Specular", "Env Internal Specular", "Internal All", "Fog"],
                            e.strComposite = ["Direct Diffuse", "Direct Specular", "Env Diffuse", "Env Specular", "Emissive", "Light Map", "Shadow", "AO", "Normal Map", "Fog", "Tone Mapping", "Gamma Correction", "Fresnel", "Transmit Diffuse", "Transmit Specular", "Internal Specular", "TT"],
                            e.strMisc = ["CSM Layer Coloration", "Lighting With Albedo"],
                            e.compositeModeToggleList = [],
                            e.singleModeToggleList = [],
                            e.miscModeToggleList = [],
                            e.textComponentList = [],
                            e.labelComponentList = [],
                            e.textContentList = [],
                            e.hideButtonLabel = void 0,
                            e._currentColorIndex = 0,
                            e.strColor = ["<color=#ffffff>", "<color=#000000>", "<color=#ff0000>", "<color=#00ff00>", "<color=#0000ff>"],
                            e.color = [a.WHITE, a.BLACK, a.RED, a.GREEN, a.BLUE],
                            e
                    }
                    o(e, t);
                    var s = e.prototype;
                    return s.start = function () {
                        if (this.node.parent.getComponent(g)) {
                            var t = this.node.parent.getComponent(h)
                                , e = .5 * t.width
                                , o = .5 * t.height
                                , i = .1 * e - e
                                , n = o - .1 * o
                                , s = this.node.getChildByName("MiscMode")
                                , l = p(s);
                            l.parent = this.node,
                                l.name = "Buttons";
                            var r = p(s);
                            r.parent = this.node,
                                r.name = "Titles";
                            for (var u = 0; u < 2; u++) {
                                var L = p(this.EnableAllCompositeModeButton.getChildByName("Label"));
                                L.setPosition(i + (u > 0 ? 450 : 150), n, 0),
                                    L.setScale(.75, .75, .75),
                                    L.parent = r;
                                var f = L.getComponent(c);
                                f.string = u ? "----------Composite Mode----------" : "----------Single Mode----------",
                                    f.color = a.WHITE,
                                    f.overflow = 0,
                                    this.labelComponentList[this.labelComponentList.length] = f
                            }
                            n -= 20;
                            for (var M = 0, b = 0; b < this.strSingle.length; b++,
                                M++) {
                                b === this.strSingle.length >> 1 && (i += 200,
                                    M = 0);
                                var v = b ? p(this.singleModeToggle) : this.singleModeToggle;
                                v.setPosition(i, n - 20 * M, 0),
                                    v.setScale(.5, .5, .5),
                                    v.parent = this.singleModeToggle.parent;
                                var T = v.getComponentInChildren(C);
                                T.string = this.strSingle[b],
                                    this.textComponentList[this.textComponentList.length] = T,
                                    this.textContentList[this.textContentList.length] = T.string,
                                    v.on(d.EventType.TOGGLE, this.toggleSingleMode, this),
                                    this.singleModeToggleList[b] = v
                            }
                            i += 200,
                                this.EnableAllCompositeModeButton.setPosition(i + 15, n, 0),
                                this.EnableAllCompositeModeButton.setScale(.5, .5, .5),
                                this.EnableAllCompositeModeButton.on(m.EventType.CLICK, this.enableAllCompositeMode, this),
                                this.EnableAllCompositeModeButton.parent = l;
                            var S = this.EnableAllCompositeModeButton.getComponentInChildren(c);
                            this.labelComponentList[this.labelComponentList.length] = S;
                            var x = p(this.EnableAllCompositeModeButton);
                            x.setPosition(i + 90, n, 0),
                                x.setScale(.5, .5, .5),
                                x.on(m.EventType.CLICK, this.changeTextColor, this),
                                x.parent = l,
                                (S = x.getComponentInChildren(c)).string = "TextColor",
                                this.labelComponentList[this.labelComponentList.length] = S;
                            var E = p(this.EnableAllCompositeModeButton);
                            E.setPosition(i + 200, n, 0),
                                E.setScale(.5, .5, .5),
                                E.on(m.EventType.CLICK, this.hideUI, this),
                                E.parent = this.node.parent,
                                (S = E.getComponentInChildren(c)).string = "Hide UI",
                                this.labelComponentList[this.labelComponentList.length] = S,
                                this.hideButtonLabel = S,
                                n -= 40;
                            for (var I = 0; I < this.strMisc.length; I++) {
                                var A = p(this.compositeModeToggle);
                                A.setPosition(i, n - 20 * I, 0),
                                    A.setScale(.5, .5, .5),
                                    A.parent = s;
                                var y = A.getComponentInChildren(C);
                                y.string = this.strMisc[I],
                                    this.textComponentList[this.textComponentList.length] = y,
                                    this.textContentList[this.textContentList.length] = y.string,
                                    A.getComponent(d).isChecked = !!I,
                                    A.on(d.EventType.TOGGLE, I ? this.toggleLightingWithAlbedo : this.toggleCSMColoration, this),
                                    this.miscModeToggleList[I] = A
                            }
                            n -= 150;
                            for (var D = 0; D < this.strComposite.length; D++) {
                                var B = D ? p(this.compositeModeToggle) : this.compositeModeToggle;
                                B.setPosition(i, n - 20 * D, 0),
                                    B.setScale(.5, .5, .5),
                                    B.parent = this.compositeModeToggle.parent;
                                var w = B.getComponentInChildren(C);
                                w.string = this.strComposite[D],
                                    this.textComponentList[this.textComponentList.length] = w,
                                    this.textContentList[this.textContentList.length] = w.string,
                                    B.on(d.EventType.TOGGLE, this.toggleCompositeMode, this),
                                    this.compositeModeToggleList[D] = B
                            }
                        } else
                            console.error("debug-view-runtime-control should be child of Canvas")
                    }
                        ,
                        s.isTextMatched = function (t, e) {
                            var o = new String(t)
                                , i = o.search(">");
                            return -1 === i ? t === e : (o = (o = o.substr(i + 1)).substr(0, o.search("<"))) === e
                        }
                        ,
                        s.toggleSingleMode = function (t) {
                            for (var e = u.root.debugView, o = t.getComponentInChildren(C), i = 0; i < this.strSingle.length; i++)
                                this.isTextMatched(o.string, this.strSingle[i]) && (e.singleMode = i)
                        }
                        ,
                        s.toggleCompositeMode = function (t) {
                            for (var e = u.root.debugView, o = t.getComponentInChildren(C), i = 0; i < this.strComposite.length; i++)
                                this.isTextMatched(o.string, this.strComposite[i]) && e.enableCompositeMode(i, t.isChecked)
                        }
                        ,
                        s.toggleLightingWithAlbedo = function (t) {
                            u.root.debugView.lightingWithAlbedo = t.isChecked
                        }
                        ,
                        s.toggleCSMColoration = function (t) {
                            u.root.debugView.csmLayerColoration = t.isChecked
                        }
                        ,
                        s.enableAllCompositeMode = function (t) {
                            var e = u.root.debugView;
                            e.enableAllCompositeMode(!0);
                            for (var o = 0; o < this.compositeModeToggleList.length; o++) {
                                this.compositeModeToggleList[o].getComponent(d).isChecked = !0
                            }
                            var i = this.miscModeToggleList[0].getComponent(d);
                            i.isChecked = !1,
                                e.csmLayerColoration = !1,
                                (i = this.miscModeToggleList[1].getComponent(d)).isChecked = !0,
                                e.lightingWithAlbedo = !0
                        }
                        ,
                        s.hideUI = function (t) {
                            var e = this.node.getChildByName("Titles")
                                , o = !e.active;
                            this.singleModeToggleList[0].parent.active = o,
                                this.miscModeToggleList[0].parent.active = o,
                                this.compositeModeToggleList[0].parent.active = o,
                                this.EnableAllCompositeModeButton.parent.active = o,
                                e.active = o,
                                this.hideButtonLabel.string = o ? "Hide UI" : "Show UI"
                        }
                        ,
                        s.changeTextColor = function (t) {
                            this._currentColorIndex++,
                                this._currentColorIndex >= this.strColor.length && (this._currentColorIndex = 0);
                            for (var e = 0; e < this.textComponentList.length; e++)
                                this.textComponentList[e].string = this.strColor[this._currentColorIndex] + this.textContentList[e] + "</color>";
                            for (var o = 0; o < this.labelComponentList.length; o++)
                                this.labelComponentList[o].color = this.color[this._currentColorIndex]
                        }
                        ,
                        s.onLoad = function () { }
                        ,
                        s.update = function (t) { }
                        ,
                        e
                }(L)).prototype, "compositeModeToggle", [M], {
                    configurable: !0,
                    enumerable: !0,
                    writable: !0,
                    initializer: function () {
                        return null
                    }
                }),
                    E = e(S.prototype, "singleModeToggle", [b], {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                        initializer: function () {
                            return null
                        }
                    }),
                    I = e(S.prototype, "EnableAllCompositeModeButton", [v], {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                        initializer: function () {
                            return null
                        }
                    }),
                    T = S)) || T));
            s._RF.pop()
        }
    }
}
));

System.register("chunks:///_virtual/i18n.ts", ["cc"], (function () {
    var t;
    return {
        setters: [function (c) {
            t = c.cclegacy
        }
        ],
        execute: function () {
            t._RF.push({}, "4b132VCpyNAybkdgZh4ZG/Z", "i18n", void 0),
                t._RF.pop()
        }
    }
}
));

System.register("chunks:///_virtual/main", ["./debug-view-runtime-control.ts", "./Benchmark.ts", "./CustomSorts.ts", "./ChangeScene.ts", "./Play.ts", "./RandomMovePlayer.ts", "./SetZ.ts", "./ccc_ext.ts", "./ccc_renderer_patch.ts", "./i18n.ts", "./render-graph.ts", "./sorting-2d.ts", "./sorting2d_setting.ts", "./sorts.ts", "./utils.ts"], (function () {
    return {
        setters: [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null],
        execute: function () { }
    }
}
));



System.register("chunks:///_virtual/RandomMovePlayer.ts", ["./rollupPluginModLoBabelHelpers.js", "cc"], (function (t) {
    var e, i, o, n, r, s;
    return {
        setters: [function (t) {
            e = t.inheritsLoose
        }
            , function (t) {
                i = t.cclegacy,
                    o = t._decorator,
                    n = t.view,
                    r = t.Vec3,
                    s = t.Component
            }
        ],
        execute: function () {
            var a;
            i._RF.push({}, "4ca7bFaLRNKrKrfPMt3gIrm", "RandomMovePlayer", void 0);
            var h = o.ccclass
                , c = (o.property,
                    t("RandomMovePlayer", h("RandomMovePlayer")(a = function (t) {
                        function i() {
                            for (var e, i = arguments.length, o = new Array(i), n = 0; n < i; n++)
                                o[n] = arguments[n];
                            return (e = t.call.apply(t, [this].concat(o)) || this)._speed = 10,
                                e._direction = new r,
                                e._time = 0,
                                e._moving = !1,
                                e
                        }
                        e(i, t);
                        var o = i.prototype;
                        return o.start = function () {
                            var t = n.getVisibleSize();
                            this.node.setPosition(Math.random() * t.width - t.width / 2, Math.random() * t.height - t.height / 2, 0)
                        }
                            ,
                            o._reset = function () {
                                var t = n.getVisibleSize()
                                    , e = Math.random() * t.width - t.width / 2
                                    , i = Math.random() * t.height - t.height / 2;
                                this._direction.set(e, i, 0).subtract(this.node.getPosition()).normalize(),
                                    this._time = 10 * Math.random()
                            }
                            ,
                            o.update = function (t) {
                                this._moving ? (this.node.getPosition(c),
                                    d.set(this._direction.x, this._direction.y, 0),
                                    c.add(d.multiplyScalar(this._speed * t)),
                                    this.node.setPosition(c),
                                    this._time -= t,
                                    this._time < 0 && (this._moving = !1)) : (this._reset(),
                                        this._moving = !0)
                            }
                            ,
                            i
                    }(s)) || a),
                    new r)
                , d = new r;
            i._RF.pop()
        }
    }
}
));

System.register("chunks:///_virtual/render-graph.ts", ["./rollupPluginModLoBabelHelpers.js", "cc", "./sorts.ts"], (function (r) {
    var t, e, n, i, o, s, a;
    return {
        setters: [function (r) {
            t = r.inheritsLoose,
                e = r.createForOfIteratorHelperLoose,
                n = r.createClass
        }
            , function (r) {
                i = r.cclegacy,
                    o = r.Pool,
                    s = r.Vec3
            }
            , function (r) {
                a = r.SortFactory
            }
        ],
        execute: function () {
            i._RF.push({}, "55595DfUDJFU58ju8ONPKl3", "render-graph", void 0);
            var u = function (r) {
                return r[r.Entity = 0] = "Entity",
                    r[r.Group = 1] = "Group",
                    r[r.Layer = 2] = "Layer",
                    r
            }(u || {})
                , l = r("SortingInfo", (function () {
                    this.level = 0,
                        this.sortingLayer = 0,
                        this.sortingOrder = 0,
                        this.type = u.Entity
                }
                ))
                , c = r("RenderEntity", function (r) {
                    function e() {
                        for (var t, e = arguments.length, n = new Array(e), i = 0; i < e; i++)
                            n[i] = arguments[i];
                        return (t = r.call.apply(r, [this].concat(n)) || this).sortingAsGroup = !1,
                            t.opacity = -1,
                            t.renderer = void 0,
                            t.group = void 0,
                            t.type = u.Entity,
                            t.exitMask = !1,
                            t
                    }
                    return t(e, r),
                        e.prototype.clear = function () {
                            this.renderer && (this.renderer.enterMask = !1),
                                this.renderer = null,
                                this.group = null,
                                this.opacity = -1,
                                this.exitMask = !1,
                                this.sortingAsGroup = !1,
                                this.level = 0,
                                this.sortingLayer = 0,
                                this.sortingOrder = 0
                        }
                        ,
                        e
                }(l))
                , g = r("RendererGroup", function (r) {
                    function i() {
                        for (var t, e = arguments.length, n = new Array(e), i = 0; i < e; i++)
                            n[i] = arguments[i];
                        return (t = r.call.apply(r, [this].concat(n)) || this).id = 0,
                            t.type = u.Group,
                            t.entities = [],
                            t
                    }
                    t(i, r);
                    var o = i.prototype;
                    return o.clear = function () {
                        for (var t, n = e(this.entities); !(t = n()).done;) {
                            var i = t.value;
                            i.type == u.Group ? _(i) : i.type == u.Entity && k(i)
                        }
                        this.entities.length = 0,
                            r.prototype.clear.call(this)
                    }
                        ,
                        o.push = function (r) {
                            this.entities.push(r),
                                h = r,
                                r.type == u.Group && (f = h)
                        }
                        ,
                        o.traverse = function (r, t) {
                            var n = this.entities;
                            r.call(t, this);
                            for (var i, o = e(n); !(i = o()).done;) {
                                var s = i.value;
                                s.type === u.Group ? s.traverse(r, t) : r.call(t, s)
                            }
                        }
                        ,
                        n(i, [{
                            key: "trail",
                            get: function () {
                                return this.entities[this.entities.length - 1]
                            }
                        }, {
                            key: "head",
                            get: function () {
                                return this.entities[0]
                            }
                        }]),
                        i
                }(c))
                , p = r("RendererLayer", function (r) {
                    function n() {
                        for (var t, e = arguments.length, n = new Array(e), i = 0; i < e; i++)
                            n[i] = arguments[i];
                        return (t = r.call.apply(r, [this].concat(n)) || this).type = u.Layer,
                            t
                    }
                    t(n, r);
                    var i = n.prototype;
                    return i.add = function (r, t, e) {
                        t ? t.push(r) : this.push(r),
                            r.group = t
                    }
                        ,
                        i.addEntity = function (r, t, e) {
                            var n;
                            return n = r.sorting2D.sortingAsGroup || r.enterMask ? function (r, t) {
                                var e = d.alloc();
                                return e.level = t,
                                    e.sortingLayer = r.sorting2D.sortingLayer,
                                    e.sortingOrder = r.sorting2D.sortingOrder,
                                    e.level = t,
                                    e.renderer = r,
                                    e.sortingAsGroup = r.sorting2D.sortingAsGroup,
                                    e.sortingLayer = r.sorting2D.sortingLayer,
                                    e.sortingOrder = r.sorting2D.sortingOrder,
                                    e.group = null,
                                    e
                            }(r, t) : function (r, t) {
                                var e = v.alloc();
                                return e.level = t,
                                    e.renderer = r,
                                    e.sortingAsGroup = r.sorting2D.sortingAsGroup,
                                    e.sortingLayer = r.sorting2D.sortingLayer,
                                    e.sortingOrder = r.sorting2D.sortingOrder,
                                    e.group = null,
                                    e
                            }(r, t),
                                this.add(n, f, e),
                                n
                        }
                        ,
                        i.traverse = function (r, t) {
                            for (var n, i = e(this.entities); !(n = i()).done;) {
                                var o = n.value;
                                o instanceof g ? o.traverse(r, t) : r.call(t, o)
                            }
                        }
                        ,
                        n
                }(g))
                , h = null
                , f = null
                , y = null
                , v = new o((function () {
                    return new c
                }
                ), 100)
                , d = new o((function () {
                    return new g
                }
                ), 10)
                , G = new o((function () {
                    return new p
                }
                ), 10);
            function k(r) {
                r.clear(),
                    v.free(r)
            }
            function _(r) {
                r.clear(),
                    d.free(r)
            }
            function L(r) {
                r.clear(),
                    G.free(r)
            }
            var D = r("SortingManager", function () {
                function r() { }
                return r.compare = function (r, t) {
                    return r.sorting2D.ignoreSort || t.sorting2D.ignoreSort ? 0 : this._sort.compare(r, t)
                }
                    ,
                    r.pushMask = function (r) {
                        var t = r;
                        if (t) {
                            var e = t.trail;
                            if (e) {
                                for (; e && e.type == u.Group;) {
                                    var n = e.trail;
                                    if (!n)
                                        break;
                                    e = n
                                }
                                e.exitMask = !0
                            } else
                                t.exitMask = !0;
                            this._maskStack.push(r.renderer)
                        }
                    }
                    ,
                    r.popMask = function () {
                        return this._maskStack.length > 0 ? this._maskStack.pop() : null
                    }
                    ,
                    r.addEntity = function (r, t) {
                        if (!r || !r.node.activeInHierarchy)
                            return null;
                        if (f) {
                            var e = f;
                            if (e.level >= t) {
                                var n = e.group;
                                n && n.level < t ? h = f = n : (f = null,
                                    h = null)
                            }
                        }
                        return r.underGroup = !1,
                            this._sort.ready(r, this),
                            this.add(r, y, t)
                    }
                    ,
                    r.copyGroup = function (r, t) {
                        if (t && r.useDefaultSort) {
                            var e = r.sorting2D
                                , n = t.renderer.sorting2D;
                            e.sortingLayer = t.sortingLayer,
                                e.sortingOrder = t.sortingOrder,
                                e.ignoreSort = n.ignoreSort,
                                e.sortInGroup = n.sortInGroup
                        }
                        r.underGroup = !0
                    }
                    ,
                    r.add = function (r, t, n) {
                        var i = h
                            , o = null;
                        h && i.group && i.type != u.Group ? o = i.group : f && (o = f),
                            o && o.type == u.Group && o.level < n && this.copyGroup(r, o);
                        var s = r.sorting2D.sortingLayer;
                        if (t && t.sortingLayer != s && (t = null),
                            !t)
                            for (var a, l = e(this._layers); !(a = l()).done;) {
                                var c = a.value;
                                if (c.sortingLayer == s) {
                                    t = c;
                                    break
                                }
                            }
                        var g = null
                            , p = null;
                        return t ? g = t.addEntity(r, n, t) : (p = function (r, t) {
                            var e = G.alloc();
                            return e.level = t,
                                e.sortingLayer = r.sorting2D.sortingLayer,
                                e
                        }(r, n),
                            this._layers.push(p),
                            g = p.addEntity(r, n, t)),
                            y = t,
                            g
                    }
                    ,
                    r.clear = function () {
                        for (var r, t = e(this._layers); !(r = t()).done;) {
                            L(r.value)
                        }
                        this._layers.length = 0,
                            this._maskStack.length = 0,
                            y = null,
                            h = null,
                            f = null
                    }
                    ,
                    r.traverse = function (r, t) {
                        this.sorting();
                        for (var n, i = e(this._layers); !(n = i()).done;) {
                            n.value.traverse(r, t)
                        }
                        this.clear()
                    }
                    ,
                    r.sortGroup = function (r) {
                        var t = this
                            , n = r.head
                            , i = r.entities;
                        i.sort((function (r, e) {
                            var i = r.type === u.Group
                                , o = e.type === u.Group;
                            if (r === n && (!i || o && r.sortingAsGroup))
                                return -1;
                            if (e === n && (!o || i && e.sortingAsGroup))
                                return 1;
                            var s = r.sortingOrder - e.sortingOrder;
                            return 0 === s ? t.compare(r.renderer, e.renderer) : s
                        }
                        ));
                        for (var o, s = e(i); !(o = s()).done;) {
                            var a = o.value;
                            a.type === u.Group && this.sortGroup(a)
                        }
                    }
                    ,
                    r.sorting = function () {
                        var t = this;
                        this._layers.sort((function (r, t) {
                            return r.sortingLayer - t.sortingLayer
                        }
                        ));
                        for (var n, i = e(this._layers); !(n = i()).done;) {
                            var o = n.value;
                            o.entities.sort((function (r, e) {
                                var n = r.sortingOrder - e.sortingOrder;
                                return 0 == n ? t.compare(r.renderer, e.renderer) : n
                            }
                            ));
                            for (var s, a = e(o.entities); !(s = a()).done;) {
                                (g = s.value).type == u.Group && this.sortGroup(g)
                            }
                            for (var l, c = e(o.entities); !(l = c()).done;) {
                                var g;
                                (g = l.value).renderer.enterMask && r.pushMask(g)
                            }
                        }
                    }
                    ,
                    n(r, null, [{
                        key: "settings",
                        get: function () {
                            return this._setting
                        },
                        set: function (r) {
                            (this._setting != r || this._setting && r.sortingMode == this._setting.sortingMode) && (this._setting = r,
                                this._sort = a.getSort(r.sortingMode))
                        }
                    }]),
                    r
            }());
            D.renderRoot = null,
                D.camera = null,
                D.center = new s,
                D._maskStack = [],
                D._layers = [],
                D._setting = void 0,
                D._sort = null,
                i._RF.pop()
        }
    }
}
));

System.register("chunks:///_virtual/SetZ.ts", ["./rollupPluginModLoBabelHelpers.js", "cc"], (function (t) {
    var e, r, o, i, n, s, c, p;
    return {
        setters: [function (t) {
            e = t.applyDecoratedDescriptor,
                r = t.inheritsLoose,
                o = t.initializerDefineProperty,
                i = t.assertThisInitialized,
                n = t.createClass
        }
            , function (t) {
                s = t.cclegacy,
                    c = t._decorator,
                    p = t.Component
            }
        ],
        execute: function () {
            var a, u, l;
            s._RF.push({}, "c2a80CsslVPWrfuEx6IIGm4", "SetZ", void 0);
            var y = c.ccclass
                , f = c.property;
            t("SetZ", y("SetZ")((l = e((u = function (t) {
                function e() {
                    for (var e, r = arguments.length, n = new Array(r), s = 0; s < r; s++)
                        n[s] = arguments[s];
                    return e = t.call.apply(t, [this].concat(n)) || this,
                        o(e, "_z", l, i(e)),
                        e
                }
                return r(e, t),
                    n(e, [{
                        key: "z",
                        get: function () {
                            return this._z
                        },
                        set: function (t) {
                            this._z = t,
                                this.node.setPosition(this.node.position.x, this.node.position.y, t)
                        }
                    }]),
                    e
            }(p)).prototype, "_z", [f], {
                configurable: !0,
                enumerable: !0,
                writable: !0,
                initializer: function () {
                    return 0
                }
            }),
                e(u.prototype, "z", [f], Object.getOwnPropertyDescriptor(u.prototype, "z"), u.prototype),
                a = u)) || a);
            s._RF.pop()
        }
    }
}
));









(function (r) {
    r('virtual:///prerequisite-imports/main', 'chunks:///_virtual/main');
}
)(function (mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
        return {
            setters: [function (_m) {
                var _exportObj = {};

                for (var _key in _m) {
                    if (_key !== "default" && _key !== "__esModule")
                        _exportObj[_key] = _m[_key];
                }

                _export(_exportObj);
            }
            ],
            execute: function () { }
        };
    });
});
