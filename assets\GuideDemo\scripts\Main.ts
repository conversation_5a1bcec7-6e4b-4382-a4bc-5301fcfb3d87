import { _decorator, Component, director } from 'cc';
import { GuideData, GuideSignType, GuideType, UserGuide } from './UserGuide';

const { ccclass, property } = _decorator;

/**
 * 主场景控制器
 * 负责管理主场景的逻辑和用户引导
 */
@ccclass('Main')
export class Main extends Component {

    // 引导数据
    private guideData: GuideData = {
        nodesAndTexts: [
            {
                path: "Canvas/Button1",
                guideText: "按钮1是Canvas的子节点，请点击~",
                guideType: GuideType.TOUCH
            },
            {
                path: "Canvas/Button1/Button2",
                guideText: "按钮2是按钮1的子节点，遮罩大小会自动根据被点击节点的大小进行调整，请点击~",
                guideType: GuideType.TOUCH
            },
            {
                path: "Canvas/Button1/Button2/Button3",
                guideText: "按钮3又是按钮2的子节点，请点击~ 引导文本框会根据文本长度来进行调整，比如这条引导有很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多很多文字",
                guideType: GuideType.TOUCH
            },
            {
                key: "71",
                guideText: "该插件还支持按键引导，现在请按下G键~",
                guideType: GuideType.KEY
            },
            {
                key: "17+71",
                guideText: "也支持组合键，按下Ctrl+G键吧~",
                guideType: GuideType.KEY
            },
            {
                path: "Canvas/Button1/Button2/Button3/cocos",
                guideText: "这张图片是按钮3的子节点，请点击~",
                guideType: GuideType.TOUCH
            },
            {
                path: "Canvas/cocos",
                guideText: "这张图片是Canvas的子节点，请点击~",
                guideType: GuideType.TOUCH
            },
            {
                path: "Canvas/cocos/cocos",
                guideText: "这张图片是上方图片的子节点，这张图片的scale值是1.5，请点击~",
                guideType: GuideType.TOUCH
            },
            {
                path: "Canvas/Label",
                guideText: "这个文本是Canvas的子节点，请点击~",
                guideType: GuideType.TOUCH
            },
            {
                path: "Canvas/Label/Label-001",
                guideText: "这个文本是上方文本的子节点，请点击~",
                guideType: GuideType.TOUCH
            },
            {
                path: "Canvas/Label/Label-001/Label-002",
                guideText: "这个文本是上方文本的子节点，请点击~",
                guideType: GuideType.TOUCH
            },
            {
                guideText: "如果没有要被点击的节点，引导文本就会显示在屏幕中间，此时用户点击屏幕就可以显示下一次引导。",
                guideType: GuideType.ONLY_TEXT
            },
            {
                path: "Canvas/Button",
                guideText: "最后这个按钮是Canvas的子节点，点击并开始下一个场景的新手引导吧~",
                guideType: GuideType.TOUCH
            }
        ],
        guideSignType: GuideSignType.FRAME
    };

    /**
     * 组件启动时调用
     * 开始显示用户引导
     */
    start(): void {
        // 获取用户引导组件并开始第一步引导
        let userGuide = this.node.getComponent(UserGuide);
        if (!userGuide) {
            userGuide = this.node.addComponent(UserGuide);
        }
        userGuide.setGuideData(this.guideData);
        userGuide.showGuide(1, true);
    }

    /**
     * 跳转到另一个场景
     * 加载场景"GuideDemo-001"
     */
    goToAnotherScene(): void {
        director.loadScene("GuideDemo-001");
    }

    /**
     * 每帧更新
     * @param deltaTime 帧间隔时间
     */
    update(deltaTime: number): void {
        // 每帧更新逻辑
    }
}
