import { BlockInputEvents, instantiate, Layers, Node, Widget } from "cc";
import { ViewUtil } from "../../utils/ViewUtil";
import { Notify } from "./Notify";

/** 提示预制件路径 - 用于加载提示预制件 */
const ToastPrefabPath: string = 'frameworkCommon/prefab/notify';
/** 等待预制件路径 - 用于加载等待预制件 */
const WaitPrefabPath: string = 'frameworkCommon/prefab/wait';

/**
 * <AUTHOR>
 * @data 2025-03-13 15:57
 * @filePath assets\core\manager\ui\LayerNotify.ts
 * @description 通知层 - 负责管理游戏中的提示和等待界面
 * 支持多种提示类型：等待提示、渐隐飘过提示等
 * 提供统一的提示管理，包括显示、隐藏和自动清理功能
 */
export class LayerNotify extends Node {

    /** 触摸事件阻挡组件 - 用于阻止点击穿透 */
    private black!: BlockInputEvents;

    /** 等待提示节点 - 用于显示加载等待界面 */
    private wait: Node = null!;

    /** 提示容器节点 - 用于管理所有提示实例 */
    private notify: Node = null!;

    /** 提示预制件节点 - 用于创建新的提示实例 */
    private notifyItem: Node = null!;

    /**
     * 构造函数
     * @param name 层名称
     * @description 初始化通知层，设置全屏适配
     */
    constructor(name: string) {
        super(name);

        // 添加Widget组件实现全屏适配
        const widget: Widget = this.addComponent(Widget);
        widget.isAlignLeft = widget.isAlignRight = widget.isAlignTop = widget.isAlignBottom = true;
        widget.left = widget.right = widget.top = widget.bottom = 0;
        widget.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;
        widget.enabled = true;
        this.init();
    }

    /**
     * 初始化通知层
     * @description 设置层级、加载预制件、初始化组件
     */
    private init(): void {
        // 设置UI层级
        this.layer = Layers.Enum.UI_2D;
        // 添加触摸阻挡组件
        this.black = this.addComponent(BlockInputEvents);
        this.black.enabled = false;
        // 异步加载等待预制件
        ViewUtil.createPrefabNodeAsync(WaitPrefabPath).then(res => this.wait = res);
        // 异步加载提示预制件
        ViewUtil.createPrefabNodeAsync(ToastPrefabPath).then(res => {
            this.notify = res;
            this.notifyItem = this.notify.children[0];
            this.notifyItem.parent = null;
        });
    }

    /**
     * 打开等待提示
     * @description 显示等待界面并启用触摸阻挡
     */
    public waitOpen(): void {
        // if (null == this.wait) this.wait = ViewUtil.createPrefabNode(WaitPrefabPath);
        this.addChild(this.wait);
        this.black.enabled = true;
    }

    /**
     * 关闭等待提示
     * @description 隐藏等待界面并禁用触摸阻挡
     */
    public waitClose(): void {
        if (this.wait && this.wait.parent) {
            this.wait.removeFromParent();
            this.black.enabled = false;
        }
    }

    /**
     * 显示渐隐飘过提示
     * @param content 提示文本内容
     * @description 创建并显示一个渐隐飘过的提示，支持多个提示同时显示
     * 当提示数量超过3个时，自动移除最早的提示
     */
    public toast(content: string): void {
        // 确保提示预制件已加载
        // if (this.notify == null) {
        //     this.notify = ViewUtil.createPrefabNode(ToastPrefabPath);
        //     this.notifyItem = this.notify.children[0];
        //     this.notifyItem.parent = null;
        // }

        // 创建新的提示实例
        this.notify.parent = this;
        let childNode = instantiate(this.notifyItem);
        let prompt = childNode.getChildByName("prompt")!;
        let toastCom = prompt.getComponent(Notify)!;
        childNode.parent = this.notify;

        // 设置提示完成回调
        toastCom.onComplete = () => {
            if (this.notify.children.length == 0) {
                this.notify.parent = null;
            }
        };
        // 设置提示内容
        toastCom.lab_content.string = content;

        // 限制最大提示数量为3个
        if (this.notify.children.length > 3) {
            this.notify.children[0].destroy();
        }
    }
}