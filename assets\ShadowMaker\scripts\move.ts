import { _decorator, Component, UITransform, view } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 移动组件
 * 用于实现节点的持续移动效果，支持上下移动和左右移动
 */
@ccclass('move')
export class Move extends Component {
    /**
     * 是否上下移动（true为上下移动，false为左右移动）
     */
    @property
    public isBottomTop: boolean = true;

    /**
     * 节点高度
     */
    private nodeHeight: number = 0;

    /**
     * 节点宽度
     */
    private nodeWidth: number = 0;

    /**
     * 移动速度
     */
    @property
    public moveSpeed: number = 5;

    /**
     * 组件启动时调用
     */
    protected start(): void {
        const transform = this.node.getComponent(UITransform);
        if (transform) {
            this.nodeHeight = transform.height;
            this.nodeWidth = transform.width;
        }
    }

    /**
     * 每帧更新
     * @param deltaTime 帧间隔时间
     */
    protected update(deltaTime: number): void {
        if (this.isBottomTop) {
            // 上下移动
            this.node.setPosition(this.node.position.x, this.node.position.y + this.moveSpeed);
            const visibleSize = view.getVisibleSize();
            
            // 检查是否超出上边界
            if (this.node.position.y - this.nodeHeight / 2 > visibleSize.height / 2) {
                this.node.setPosition(
                    this.node.position.x,
                    -visibleSize.height / 2 - this.nodeHeight / 2
                );
            }
        } else {
            // 左右移动
            this.node.setPosition(this.node.position.x + this.moveSpeed, this.node.position.y);
            const visibleSize = view.getVisibleSize();
            
            // 检查是否超出右边界
            if (this.node.position.x - this.nodeWidth / 2 > visibleSize.width / 2) {
                this.node.setPosition(
                    -visibleSize.width / 2 - this.nodeWidth / 2,
                    this.node.position.y
                );
            }
        }
    }
} 