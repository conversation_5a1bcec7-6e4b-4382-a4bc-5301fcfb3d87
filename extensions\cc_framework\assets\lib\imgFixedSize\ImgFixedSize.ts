import { _decorator, Component, Node, Size, size, Sprite, SpriteFrame, UITransform } from 'cc';
const { ccclass, menu, property } = _decorator;

/**
 * 图片固定尺寸组件
 * @description 自动调整图片尺寸的组件，支持等比例缩放和异形图片适配
 * 可以设置固定尺寸、反转方向，并自动处理图片尺寸变化
 * 适用于需要保持特定尺寸的UI图片元素
 */
@ccclass('ImgFixedSize')
@menu('自定义工具/自动匹配图片尺寸')
export class ImgFixedSize extends Component {

    /** Sprite组件引用 */
    @property
    private _icon: Sprite = null!;
    @property({ type: Sprite, visible: false })
    private get icon(): Sprite {
        if (!this._icon) {
            this._icon = this.node.getComponent(Sprite) || this.node.addComponent(Sprite);
            this._icon.trim = true;
            this._icon.sizeMode = Sprite.SizeMode.TRIMMED;
        }
        return this._icon;
    }

    /** UITransform组件引用 */
    @property
    private _transform: UITransform = null!;
    @property({ type: UITransform, visible: false })
    private get transform(): UITransform {
        if (!this._transform) {
            this._transform = this.node.getComponent(UITransform) || this.node.addComponent(UITransform);
        }
        return this._transform;
    }

    /** 当前精灵帧 */
    @property
    private _spriteFrame: SpriteFrame | null = null;
    @property({ type: SpriteFrame, visible: false })
    public get spriteFrame(): SpriteFrame | null {
        if (this._spriteFrame !== this.icon.spriteFrame) {
            this._spriteFrame = this.icon.spriteFrame;
            this.onSizeChanged();
        }
        return this._spriteFrame;
    }

    /** 是否适配异形图片 */
    @property
    private _adapt: boolean = false;
    @property({ displayName: '适配异形图片' })
    private get adapt(): boolean {
        return this._adapt;
    }
    private set adapt(value: boolean) {
        if (value == this._adapt) return;
        this._adapt = value;
        this.onSizeChanged();
    }

    /** 固定尺寸（正方形） */
    @property
    private _fixedSize: number = 1;
    @property({ displayName: "固定尺寸(正方体)", min: 1, visible: function (this: ImgFixedSize) { return !this.adapt } })
    public get fixedSize(): number {
        return this._fixedSize;
    }
    public set fixedSize(value: number) {
        this._fixedSize = value;
        this.onSizeChanged();
    }

    /** 固定尺寸（宽高不等） */
    @property
    private _fixedVecSize: Size = size(1, 1);
    @property({ type: Size, displayName: "固定尺寸(宽高不等)", visible: function (this: ImgFixedSize) { return this.adapt } })
    private get fixedVecSize(): Size {
        return this._fixedVecSize;
    }
    private set fixedVecSize(value: Size) {
        this._fixedVecSize = value;
        this.onSizeChanged();
    }

    /** 是否水平反转 */
    @property
    private _reversalX: boolean = false;
    @property({ displayName: "反转x" })
    private get reversalX(): boolean {
        return this._reversalX;
    }
    private set reversalX(value: boolean) {
        if (value == this._reversalX) return;
        this._reversalX = value;
        this.onSizeChanged();
    }

    /** 是否垂直反转 */
    @property
    private _reversalY: boolean = false;
    @property({ displayName: "反转y" })
    private get reversalY(): boolean {
        return this._reversalY;
    }
    private set reversalY(value: boolean) {
        if (value == this.reversalY) return;
        this._reversalY = value;
        this.onSizeChanged();
    }

    /**
     * 组件加载时的初始化
     * @description 初始化组件属性，注册尺寸变化事件监听
     */
    protected onLoad(): void {
        this._fixedSize = this.fixedSize
        this.node.on(Node.EventType.SIZE_CHANGED, this.onSizeChanged, this);
        this.onSizeChanged();
    }

    /**
     * 编辑器中的重置方法
     * @description 重置所有属性为默认值
     */
    public resetInEditor(): void {
        this._adapt = false;
        this._fixedSize = 1;
        this._fixedVecSize.width = 1;
        this._fixedVecSize.height = 1;
        this._reversalX = false;
        this._reversalY = false;
        this._spriteFrame = null;
    }

    /**
     * 处理尺寸变化
     * @description 根据当前设置计算并应用新的缩放比例
     * 支持等比例缩放和异形图片适配两种模式
     */
    private onSizeChanged(): void {
        let scaleSize = this.adapt ? Math.min(this.fixedVecSize.width / this.transform.width, this.fixedVecSize.height / this.transform.height) : this.fixedSize / Math.max(this.transform.width, this.transform.height);
        this.node.setScale(this.reversalX ? scaleSize * -1 : scaleSize, this.reversalY ? scaleSize * -1 : scaleSize);
    }
}