import { _decorator, Component, EditBox, instantiate, math, Node, Toggle, ToggleContainer, UITransform, Vec3 } from 'cc';
import { BezierMove, ControlPointSideType, EasingType } from './BezierMove';
import { Monster } from './Monster';
const { ccclass, property } = _decorator;

/**
 * 游戏管理器组件
 * 用于管理贝塞尔曲线移动的演示场景
 */
@ccclass('GameManager')
export class GameManager extends Component {
    @property(Node)
    private ndSword: Node | null = null;

    @property({ tooltip: '剑的数量' })
    private _numberOfSwords: number = 3;

    @property(EditBox)
    private countOfSwords: EditBox | null = null;

    @property(EditBox)
    private speedOfSwords: EditBox | null = null;

    @property(Toggle)
    private showTrace: Toggle | null = null;

    @property([Node])
    private swordList: Node[] = [];

    @property(ToggleContainer)
    private controlSide: ToggleContainer | null = null;

    @property(ToggleContainer)
    private offset: ToggleContainer | null = null;

    @property(ToggleContainer)
    private easingControl: ToggleContainer | null = null;

    @property(ToggleContainer)
    private rotationSmooth: ToggleContainer | null = null;

    @property(ToggleContainer)
    private mode: ToggleContainer | null = null;

    @property(Monster)
    private monster: Monster | null = null;

    private _selectedControlSide: ControlPointSideType = ControlPointSideType.Left;
    private _offset: number = 0.5;
    private _isRandomOffset: boolean = false;
    private _selectedEasingType: EasingType = EasingType.linear;
    private _speed: number = 600;
    private _rotationSmooth: number = 0.6;
    private _isMouseMode: boolean = true;

    /**
     * 剑的数量
     */
    public get numberOfSwords(): number {
        return this._numberOfSwords;
    }

    public set numberOfSwords(value: number) {
        if (!Number.isNaN(value)) {
            console.log("set numberOfSwords:", value);
            this._numberOfSwords = value;
            this.createSwords(this._numberOfSwords);
        }
    }

    /**
     * 组件启动时调用
     */
    protected start(): void {
        // 添加初始剑到列表
        this.swordList.push(this.ndSword!);

        // 创建指定数量的剑
        this.createSwords(this.numberOfSwords);

        // 设置剑数量显示
        this.countOfSwords!.string = this.numberOfSwords.toString();
        this.speedOfSwords!.string = this._speed.toString();

        // 注册事件
        this.registerEvent();

        // 开始追踪怪物
        this.scheduleTrackMonster();
    }

    /**
     * 组件销毁时调用
     */
    protected onDestroy(): void {
        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
    }

    /**
     * 创建指定数量的剑
     * @param count 要创建的剑的数量
     */
    private createSwords(count: number): void {
        this.deleteSwords();
        for (let i = 0; i < count - 1; i++) {
            const sword = instantiate(this.ndSword!);
            sword.setParent(this.node);
            sword.angle = math.randomRangeInt(-60, 60);
            this.swordList.push(sword);
        }
    }

    /**
     * 删除所有剑（除了原始剑）
     */
    private deleteSwords(): void {
        for (let i = 0; i < this.swordList.length; i++) {
            if (this.swordList[i] !== this.ndSword) {
                this.swordList[i].destroy();
            }
        }
        this.swordList.length = 0;
        this.swordList.push(this.ndSword!);
    }

    /**
     * 触摸结束事件处理
     * @param event 触摸事件
     */
    private onTouchEnd(event: any): void {
        if (this._isMouseMode) {
            const touchPos = event.getUILocation();
            const worldPos = this.convertToWorldPosition(touchPos.x, touchPos.y);
            this.moveTo(worldPos);
        } else {
            this.monster!.setRandomDirection();
        }
    }

    /**
     * 将屏幕坐标转换为世界坐标
     * @param x 屏幕X坐标
     * @param y 屏幕Y坐标
     * @returns 世界坐标
     */
    private convertToWorldPosition(x: number, y: number): Vec3 {
        return this.node.getComponent(UITransform)!.convertToNodeSpaceAR(new Vec3(x, y, 0));
    }

    private scheduleTrackMonster(): void {
        this.schedule(() => {
            if (!this._isMouseMode) {
                const monsterPos = this.monster!.node.position.clone();
                this.moveTo(monsterPos, 0.08);
            }
        }, 0.5);
    }

    private registerEvent(): void {
        // 速度输入框事件
        this.speedOfSwords!.node.on(EditBox.EventType.TEXT_CHANGED, () => {
            this._speed = parseInt(this.speedOfSwords!.string);
        });

        // 数量输入框事件
        this.countOfSwords!.node.on(EditBox.EventType.TEXT_CHANGED, () => {
            this.numberOfSwords = parseInt(this.countOfSwords!.string);
        });

        // 触摸事件
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);

        // 显示轨迹切换事件
        this.showTrace!.node.on(Toggle.EventType.TOGGLE, () => {
            if (!this.showTrace!.isChecked) {
                for (let i = 0; i < this.swordList.length; i++) {
                    this.swordList[i].getComponent(BezierMove)?.clearTrajectory();
                }
            }
        });

        // 控制点位置切换事件
        this.controlSide!.toggleItems.forEach((toggle, index) => {
            toggle.node.on(Toggle.EventType.TOGGLE, () => {
                if (toggle.isChecked) {
                    switch (index) {
                        case 0:
                            this._selectedControlSide = ControlPointSideType.Left;
                            break;
                        case 1:
                            this._selectedControlSide = ControlPointSideType.Right;
                            break;
                        case 2:
                            this._selectedControlSide = ControlPointSideType.Random;
                            break;
                    }
                }
            });
        });

        // 偏移量切换事件
        this.offset!.toggleItems.forEach((toggle, index) => {
            toggle.node.on(Toggle.EventType.TOGGLE, () => {
                if (toggle.isChecked) {
                    switch (index) {
                        case 0:
                            this._isRandomOffset = false;
                            break;
                        case 1:
                            this._isRandomOffset = true;
                            break;
                    }
                }
            });
        });

        // 缓动类型切换事件
        this.easingControl!.toggleItems.forEach((toggle, index) => {
            toggle.node.on(Toggle.EventType.TOGGLE, () => {
                if (toggle.isChecked) {
                    switch (index) {
                        case 0:
                            this._selectedEasingType = EasingType.linear;
                            break;
                        case 1:
                            this._selectedEasingType = EasingType.quadIn;
                            break;
                        case 2:
                            this._selectedEasingType = EasingType.cubicOut;
                            break;
                        case 3:
                            this._selectedEasingType = EasingType.bounceIn;
                            break;
                    }
                }
            });
        });

        // 模式切换事件
        this.mode!.toggleItems.forEach((toggle, index) => {
            if (index === 0) {
                this.isMouseMode = toggle.isChecked;
            }
            toggle.node.on(Toggle.EventType.TOGGLE, () => {
                if (toggle.isChecked) {
                    switch (index) {
                        case 0:
                            this.isMouseMode = true;
                            break;
                        case 1:
                            this.isMouseMode = false;
                            break;
                    }
                }
            });
        });

        // 旋转平滑度切换事件
        this.rotationSmooth!.toggleItems.forEach((toggle, index) => {
            toggle.node.on(Toggle.EventType.TOGGLE, () => {
                if (toggle.isChecked) {
                    switch (index) {
                        case 0:
                            this._rotationSmooth = 0.1;
                            break;
                        case 1:
                            this._rotationSmooth = 0.5;
                            break;
                        case 2:
                            this._rotationSmooth = 1;
                            break;
                    }
                }
            });
        });
    }

    private moveTo(targetPos: Vec3, delay: number = 0): void {
        for (let i = 0; i < this.swordList.length; i++) {
            this.scheduleOnce(() => {
                if (this.swordList[i]) {
                    const bezierMove = this.swordList[i].getComponent(BezierMove);
                    if (bezierMove) {
                        bezierMove.showTrajectory = this.showTrace!.isChecked;
                        bezierMove.controlPointSide = this._selectedControlSide;
                        bezierMove.controlPointOffset = this._isRandomOffset ? Math.random() : this._offset;
                        bezierMove.easing = this._selectedEasingType;
                        bezierMove.rotationSmoothness = this._rotationSmooth;
                        bezierMove.speed = this._speed;
                        bezierMove.moveTo(targetPos);
                    }
                }
            }, i * delay);
        }
    }

    set isMouseMode(value: boolean) {
        this._isMouseMode = value;
        this.monster!.node.active = !value;
    }
} 