import { EventKeyboard, input, Input, KeyCode } from "cc";
import { Entity } from "db://framework/ECS/Entity";
import { EntitySystem } from "db://framework/ECS/systems/EntitySystem";
import { Matcher } from "db://framework/ECS/utils/Matcher";
import { Movement } from "../components/Movement";
import { PlayerInput } from "../components/PlayerInput";
import { Transform } from "../components/Transform";

/**
 * <AUTHOR>
 * @data 2025-08-02 14:48
 * @filePath assets\lawnMowerDemo_ECS\scripts\systems\PlayerInputSystem.ts
 * @description 玩家输入系统 - 处理键盘输入并控制玩家移动
 */
export class PlayerInputSystem extends EntitySystem {

    // 输入状态
    private inputState = { left: false, right: false, up: false, down: false };

    constructor() {
        super(Matcher.empty().all(Transform, Movement, PlayerInput));
        this.setupInputHandling();
    }
    /** 设置输入处理 */
    private setupInputHandling(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.on(Input.EventType.KEY_UP, this.onKeyUp, this);
    }
    protected onRemoved(entity: Entity): void {
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);
    }
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.ARROW_UP:
            case KeyCode.KEY_W:
                this.inputState.up = true;
                break;
            case KeyCode.ARROW_DOWN:
            case KeyCode.KEY_S:
                this.inputState.down = true;
                break;
            case KeyCode.ARROW_LEFT:
            case KeyCode.KEY_A:
                this.inputState.left = true;
                break;
            case KeyCode.ARROW_RIGHT:
            case KeyCode.KEY_D:
                this.inputState.right = true;
                break;
        }
    }
    private onKeyUp(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.ARROW_UP:
            case KeyCode.KEY_W:
                this.inputState.up = false;
                break;
            case KeyCode.ARROW_DOWN:
            case KeyCode.KEY_S:
                this.inputState.down = false;
                break;
            case KeyCode.ARROW_LEFT:
            case KeyCode.KEY_A:
                this.inputState.left = false;
                break;
            case KeyCode.ARROW_RIGHT:
            case KeyCode.KEY_D:
                this.inputState.right = false;
                break;
        }
    }

    protected process(entities: Entity[]): void {
        for (const entity of entities) {
            const movement = entity.getComponent(Movement);
            if (!movement) continue;

            // 重置输入方向
            movement.inputDirection.set(0, 0);

            // 根据输入状态设置移动方向
            if (this.inputState.left) movement.inputDirection.x -= 1;
            if (this.inputState.right) movement.inputDirection.x += 1;
            if (this.inputState.up) movement.inputDirection.y += 1;
            if (this.inputState.down) movement.inputDirection.y -= 1;

            // 标准化方向向量
            if (movement.inputDirection.lengthSqr() > 0) {
                movement.inputDirection.normalize();
            }
        }
    }
}