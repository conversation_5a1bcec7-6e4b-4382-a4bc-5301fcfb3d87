import { _decorator, Animation, Component, Label } from 'cc';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\core\manager\ui\Notify.ts
 * @ author: OldPoint
 * @ data: 2025-03-15 19:30
 * @ description: 滚动消息提示组件 - 用于显示游戏中的临时提示信息
 * 支持动画效果和完成回调，可自定义提示内容和样式
 */
@ccclass('Notify')
export class Notify extends Component {

    /** 提示内容文本组件 - 用于显示提示信息 */
    @property(Label)
    public lab_content: Label = null!;

    /** 动画组件 - 用于控制提示的显示和消失动画 */
    @property(Animation)
    private animation: Animation = null!;

    /** 提示动画完成回调函数 - 在提示消失后执行 */
    public onComplete: Function = null!;

    /**
     * 组件加载时初始化
     * @description 注册动画完成事件，用于处理提示消失后的清理工作
     */
    protected onLoad(): void {
        if (this.animation) {
            this.animation.on(Animation.EventType.FINISHED, this.onFinished, this);
        }
    }

    /**
     * 动画完成回调
     * @description 销毁提示节点并执行完成回调函数
     * 清理相关引用，避免内存泄漏
     */
    private onFinished(): void {
        // 销毁提示节点
        this.node.parent!.destroy();
        // 执行完成回调
        this.onComplete && this.onComplete();
        // 清理回调引用
        this.onComplete = null!;
    }

}