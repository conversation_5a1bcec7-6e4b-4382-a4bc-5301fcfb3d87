{"$schema": "./@types/schema/package/index.json", "package_version": 2, "name": "framework", "version": "1.0.0", "author": "oldp", "editor": ">=3.8.6", "scripts": {"build": "tsc", "watch": "tsc -w"}, "description": "i18n:framework.description", "main": "./dist/main.js", "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "contributions": {"inspector": {"section": {"asset": {"directory": "./dist/asset-directory.js"}}}, "asset-db": {"mount": {"path": "./assets", "readonly": false}}}, "dependencies": {"@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "minigame-api-typings": "^3.8.11"}}