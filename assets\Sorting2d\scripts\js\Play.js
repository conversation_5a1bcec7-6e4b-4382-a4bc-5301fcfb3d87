System.register("chunks:///_virtual/Play.ts", ["./rollupPluginModLoBabelHelpers.js", "cc"], (function (e) {
    var t, r, n, o, a, i, c, l, s, u, p, v, d;
    return {
        setters: [function (e) {
            t = e.applyDecoratedDescriptor,
                r = e.inherits<PERSON>oose,
                n = e.initializerDefineProperty,
                o = e.assertThisInitialized
        }
            , function (e) {
                a = e.cclegacy,
                    i = e._decorator,
                    c = e.Vec2,
                    l = e.Vec3,
                    s = e.<PERSON>,
                    u = e.CameraComponent,
                    p = e.<PERSON>de,
                    v = e.director,
                    d = e.Component
            }
        ],
        execute: function () {
            var f, y, m, g, h, b, w, P, T, C;
            a._RF.push({}, "e6115L+E5FJMpfUt6oW6YzH", "Play", void 0);
            var z = i.ccclass
                , L = i.property
                , _ = new c
                , R = new l
                , D = new l;
            e("Play", (f = z("Play"),
                y = L(s),
                m = L(u),
                g = L(p),
                f((w = t((b = function (e) {
                    function t() {
                        for (var t, r = arguments.length, a = new Array(r), i = 0; i < r; i++)
                            a[i] = arguments[i];
                        return t = e.call.apply(e, [this].concat(a)) || this,
                            n(t, "canvas", w, o(t)),
                            n(t, "worldCamera", P, o(t)),
                            n(t, "player", T, o(t)),
                            n(t, "speed", C, o(t)),
                            t.targetPos = new l,
                            t.moving = !1,
                            t
                    }
                    r(t, e),
                        t.convertTouchLocationToNode = function (e, t, r, n) {
                            var o = n ? n.camera : v.root.batcher2D.getFirstRenderCamera(t);
                            return R.set(e.x, e.y, 0),
                                o.screenToWorld(R, R),
                                t._uiProps.uiTransformComp.convertToNodeSpaceAR(R, R),
                                r || (r = new l),
                                r.set(R),
                                r
                        }
                        ;
                    var a = t.prototype;
                    return a.start = function () {
                        var e = this;
                        this.canvas.node.on(p.EventType.TOUCH_START, (function (r) {
                            r.getLocation(_),
                                t.convertTouchLocationToNode(_, e.canvas.node, R, e.worldCamera),
                                e.targetPos.set(R),
                                e.moving = !0
                        }
                        ))
                    }
                        ,
                        a.update = function (e) {
                            if (this.moving) {
                                this.player.getPosition(R);
                                var t = R;
                                D.set(this.targetPos);
                                var r = D.subtract(t);
                                if (r.length() < 10)
                                    return void (this.moving = !1);
                                r.normalize(),
                                    t.add(r.multiplyScalar(this.speed * e)),
                                    this.player.setPosition(t)
                            }
                        }
                        ,
                        t
                }(d)).prototype, "canvas", [y], {
                    configurable: !0,
                    enumerable: !0,
                    writable: !0,
                    initializer: function () {
                        return null
                    }
                }),
                    P = t(b.prototype, "worldCamera", [m], {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                        initializer: function () {
                            return null
                        }
                    }),
                    T = t(b.prototype, "player", [g], {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                        initializer: function () {
                            return null
                        }
                    }),
                    C = t(b.prototype, "speed", [L], {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                        initializer: function () {
                            return 100
                        }
                    }),
                    h = b)) || h));
            a._RF.pop()
        }
    }
}
));