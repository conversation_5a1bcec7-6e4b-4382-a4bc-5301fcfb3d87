/**
 * <AUTHOR>
 * @data 2025-04-09 14:21
 * @filePath assets\core\utils\DateUtil.ts
 * @description 日期工具类
 * @description 提供日期和时间相关的常用工具方法
 * 包括时间戳获取、日期格式化、倒计时显示等功能
 * 支持服务器时间同步和本地时间处理
 */
export class DateUtil {

    /** 服务器时间基准线，用于同步时间 */
    private static _currentDateTime: number = 0;
    /** 
     * 当前时间戳
     * @description 获取当前时间戳（毫秒），如果未同步服务器时间则返回本地时间
     * 可以通过setter设置服务器时间进行同步
     * @returns 当前时间戳（毫秒）
     */
    public static get currentDateTime(): number { return this._currentDateTime == 0 ? Date.now() : this._currentDateTime }
    public static set currentDateTime(value: number) { this._currentDateTime = value }
    /** 
     * 当前时间对象
     * @description 根据currentDateTime返回对应的Date对象
     * @returns 当前时间的Date对象
     */
    public static get currentDate(): Date { return new Date(this.currentDateTime) }
    /** 
     * 获取今天零时的时间戳
     * @description 返回今天0点0分0秒的时间戳（毫秒）
     * @returns 今天零时的时间戳
     */
    public static GetTodayZeroTime(): number { return this.currentDate.setHours(0, 0, 0, 0) }
    /** 
     * 获取今日24点时间戳
     * @description 返回今天24点0分0秒的时间戳（毫秒）
     * @returns 今日24点时间戳
     */
    public static GetToday24Time(): number { return this.currentDate.setHours(24, 0, 0, 0); }
    /** 
     * 获取今日剩余时间
     * @description 计算从当前时间到今日24点的剩余毫秒数
     * @returns 今日剩余毫秒数
     */
    public static GetTodayRemainTime(): number { return this.GetToday24Time() - this.currentDateTime; }
    /**
     * 返回指定时间的前后多少天的0点时间戳
     * @param day 天数 正数为往后，负数为往前
     * @param time 时间戳或时间对象 默认当前时间
     * @returns 指定时间的前后多少天的0点时间戳
     */
    public static GetDayZeroTime(day: number, time: number | Date = this.currentDate): number {
        const date = time instanceof Date ? time : new Date(time);
        return date.setHours(0, 0, 0, 0) + 86400000 * day;
    }
    /** 
     * 返回指定时间的前后多少天的24点时间戳
     * @param day 天数 正数为往后，负数为往前
     * @param time 时间戳或时间对象 默认当前时间
     * @returns 指定时间的前后多少天的24点时间戳
     */
    public static GetDay24Time(day: number, time: number | Date = this.currentDate): number {
        const date = time instanceof Date ? time : new Date(time);
        return date.setHours(24, 0, 0, 0) + 86400000 * day;
    }
    /**
     * 格式化时间
     * @description 将时间戳或Date对象格式化为指定格式的字符串
     * 支持自定义格式，包括年月日时分秒的任意组合
     * @param time 时间戳或时间对象，默认为当前时间
     * @param format 格式字符串，默认为"yyyy-MM-dd HH:mm:ss"
     * @param isPad 是否补零，默认为true
     * @returns 格式化后的时间字符串
     * @example
     * DateUtil.FormatTime() // 返回当前时间，格式：2024-03-15 14:30:00
     * DateUtil.FormatTime(Date.now(), "yyyy年MM月dd日") // 返回：2024年03月15日
     */
    public static FormatTime(time: number | Date | null = null, format: string = "yyyy-MM-dd HH:mm:ss", isPad: boolean = true): string {
        const date = time == null ? this.currentDate : time instanceof Date ? time : new Date(time);
        return format.replace(/yyyy/g, date.getFullYear().toString())
            .replace(/MM/g, (date.getMonth() + 1).toString().padStart(isPad ? 2 : 0, '0'))
            .replace(/dd/g, date.getDate().toString().padStart(isPad ? 2 : 0, '0'))
            .replace(/HH/g, date.getHours().toString().padStart(isPad ? 2 : 0, '0'))
            .replace(/mm/g, date.getMinutes().toString().padStart(isPad ? 2 : 0, '0'))
            .replace(/ss/g, date.getSeconds().toString().padStart(isPad ? 2 : 0, '0'));
    }
    /**
     * 格式化倒计时时间
     * @description 将秒数转换为指定格式的倒计时显示
     * 支持天、时、分、秒的任意组合显示
     * @param time 倒计时秒数
     * @param format 格式字符串，默认为"dd天HH时mm分ss秒"
     * @returns 格式化后的倒计时字符串
     * @example
     * DateUtil.FormatTimeToSecond(3661) // 返回：00天01时01分01秒
     * DateUtil.FormatTimeToSecond(3661, "HH:mm:ss") // 返回：01:01:01
     */
    public static FormatTimeToSecond(time: number, format: string = "dd天HH时mm分ss秒"): string {
        let days = 0;
        let hours = 0;
        let minutes = 0;
        let seconds = 0;

        if (format.includes("dd")) {
            // 如果格式包含天数，则按天计算
            days = Math.floor(time / 86400);
            hours = Math.floor((time % 86400) / 3600);
        } else {
            // 如果格式不包含天数，则小时数从总时间计算
            hours = Math.floor(time / 3600);
        }

        minutes = Math.floor((time % 3600) / 60);
        seconds = time % 60;

        return format.replace(/dd/g, days.toString())
            .replace(/HH/g, hours.toString().padStart(2, '0'))
            .replace(/mm/g, minutes.toString().padStart(2, '0'))
            .replace(/ss/g, seconds.toString().padStart(2, '0'));
    }
    /**
     * 判断两个时间戳是否在同一天
     * @param time1 时间戳1
     * @param time2 时间戳2
     * @returns 是否在同一天
     */
    public static IsSameDay(time1: number, time2: number): boolean {
        return new Date(time1).toDateString() === new Date(time2).toDateString();
    }

    /** 
     * 是否在指定时间的小时之间
     * @param startHours 开始小时
     * @param endHours 结束小时
     * @param time 时间戳或时间对象 默认当前时间
     * @returns 是否在指定时间的小时之间
     */
    public static IsInTime(startHours: number, endHours: number, time: number | Date = this.currentDate): boolean {
        const date = time instanceof Date ? time : new Date(time);
        return date.getHours() >= startHours && date.getHours() < endHours;
    }
}