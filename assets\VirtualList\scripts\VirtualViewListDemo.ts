import { _decorator, Component, instantiate, Label, Node, Prefab } from 'cc';
import { VirtualViewItem, VirtualViewItemData } from './VirtualViewItem';
import { Callbacks, ItemData, VirtualViewList } from './VirtualViewList';
const { ccclass, property } = _decorator;

export enum ItemType {
    GridVer = "GridVer",
    Vertical = "Vertical",
    Horizontal = "Horizontal",
    Flexible1 = "Flexible1",
    Flexible2 = "Flexible2",
    Flexible3 = "Flexible3",
    Flexible4 = "Flexible4"
}

export enum ListType {
    VerticalList = 0,
    HorizontalList = 1,
    GridVerList = 2,
    GridHorList = 3,
    Flexible = 4
}

interface TestData {
    index: number;
    type: string;
    data: {
        info?: string;
        icon?: string;
        avatar?: string;
        name?: string;
        [key: string]: any;
    };
}

@ccclass('VirtualViewListDemo')
export class VirtualViewListDemo extends Component {
    @property(VirtualViewList)
    verticalList: VirtualViewList | null = null;

    @property(VirtualViewList)
    horizontalList: VirtualViewList | null = null;

    @property(VirtualViewList)
    gridVerList: VirtualViewList | null = null;

    @property(VirtualViewList)
    gridHorList: VirtualViewList | null = null;

    @property(VirtualViewList)
    flexibleList: VirtualViewList | null = null;

    @property(Prefab)
    itemFlexible1: Prefab | null = null;

    @property(Prefab)
    itemFlexible2: Prefab | null = null;

    @property(Prefab)
    itemFlexible3: Prefab | null = null;

    @property(Prefab)
    itemFlexible4: Prefab | null = null;

    @property(Prefab)
    itemGridVer: Prefab | null = null;

    @property(Prefab)
    itemHorizontal: Prefab | null = null;

    @property(Prefab)
    itemVertical: Prefab | null = null;

    @property(Label)
    labShowLayout: Label | null = null;

    private mTestData: TestData[] = [];
    private mShowType: ListType = ListType.VerticalList;
    private mCurVirtualView: VirtualViewList | null = null;

    onLoad() {
        const callbacks: Callbacks = {
            onItemInit: (node: Node, index: number, data: ItemData) => {
                this.InitItem(node, index, data);
            },
            onItemUpdate: (node: Node, index: number, data: ItemData) => {
                this.UpdateItem(node, index, data);
            },
            onScrolling: (progress: number) => {
                this.OnScrolling(progress);
            }
        };

        if (this.verticalList) {
            this.verticalList.RegisterTemplate(ItemType.Vertical, instantiate(this.itemVertical!), true);
            this.verticalList.SetCallbacks(callbacks);
        }

        if (this.horizontalList) {
            this.horizontalList.RegisterTemplate(ItemType.Horizontal, instantiate(this.itemHorizontal!), true);
            this.horizontalList.SetCallbacks(callbacks);
        }

        if (this.gridVerList) {
            this.gridVerList.RegisterTemplate(ItemType.GridVer, instantiate(this.itemGridVer!), true);
            this.gridVerList.SetCallbacks(callbacks);
        }

        if (this.gridHorList) {
            this.gridHorList.RegisterTemplate(ItemType.GridVer, instantiate(this.itemGridVer!), true);
            this.gridHorList.SetCallbacks(callbacks);
        }

        if (this.flexibleList) {
            this.flexibleList.RegisterTemplate(ItemType.Flexible1, instantiate(this.itemFlexible1!), true);
            this.flexibleList.RegisterTemplate(ItemType.Flexible2, instantiate(this.itemFlexible2!));
            this.flexibleList.RegisterTemplate(ItemType.Flexible3, instantiate(this.itemFlexible3!));
            this.flexibleList.RegisterTemplate(ItemType.Flexible4, instantiate(this.itemFlexible4!));
            this.flexibleList.SetCallbacks(callbacks);
        }
    }

    start() {
        this.InitTestData();
        this.UpdateListShow();
        this.mCurVirtualView?.ReloadData(this.mTestData);
    }

    private InitTestData() {
        this.mTestData = [];

        if (this.mShowType !== ListType.Flexible) {
            let type = ItemType.Vertical;
            switch (this.mShowType) {
                case ListType.VerticalList:
                    type = ItemType.Vertical;
                    break;
                case ListType.HorizontalList:
                    type = ItemType.Horizontal;
                    break;
                case ListType.GridVerList:
                case ListType.GridHorList:
                    type = ItemType.GridVer;
                    break;
            }

            for (let i = 0; i < 500; i++) {
                this.mTestData.push({
                    index: i,
                    type: type,
                    data: {
                        info: `${i}`,
                        icon: "item_1"
                    }
                });
            }
        } else {
            const types = [ItemType.Flexible1, ItemType.Flexible2, ItemType.Flexible3, ItemType.Flexible4];
            for (let i = 0; i < 500; i++) {
                const type = types[Math.floor(Math.random() * types.length)];
                switch (type) {
                    case ItemType.Flexible1:
                        this.mTestData.push({
                            index: i,
                            type: type,
                            data: {
                                info: "大家好，我是隔壁老王",
                                icon: "",
                                avatar: "avatar1",
                                name: "Tom"
                            }
                        });
                        break;
                    case ItemType.Flexible2:
                        this.mTestData.push({
                            index: i,
                            type: type,
                            data: {
                                info: "欢迎来到冒险世界！这里有广阔的大陆、神秘的宝藏和无数志同道合的伙伴等待与你相遇。希望你在探索与挑战中不断成长，收获属于自己的荣耀与回忆。祝你旅途愉快，勇者前路光明！",
                                icon: "",
                                avatar: "avatar2",
                                name: "Jerry"
                            }
                        });
                        break;
                    case ItemType.Flexible3:
                        this.mTestData.push({
                            index: i,
                            type: type,
                            data: {
                                info: "",
                                icon: "item_1",
                                avatar: "avatar1",
                                name: "Tom"
                            }
                        });
                        break;
                    case ItemType.Flexible4:
                        this.mTestData.push({
                            index: i,
                            type: type,
                            data: {
                                info: "",
                                icon: "item_2",
                                avatar: "avatar2",
                                name: "Jerry"
                            }
                        });
                        break;
                }
            }
        }
    }

    private UpdateListShow() {
        switch (this.mShowType) {
            case ListType.VerticalList:
                this.labShowLayout!.string = "垂直布局";
                this.mCurVirtualView = this.verticalList;
                break;
            case ListType.HorizontalList:
                this.labShowLayout!.string = "水平布局";
                this.mCurVirtualView = this.horizontalList;
                break;
            case ListType.GridVerList:
                this.labShowLayout!.string = "网格垂直布局";
                this.mCurVirtualView = this.gridVerList;
                break;
            case ListType.GridHorList:
                this.labShowLayout!.string = "网格水平布局";
                this.mCurVirtualView = this.gridHorList;
                break;
            case ListType.Flexible:
                this.labShowLayout!.string = "灵活布局";
                this.mCurVirtualView = this.flexibleList;
                break;
        }

        this.gridVerList!.node.active = this.mShowType === ListType.GridVerList;
        this.gridHorList!.node.active = this.mShowType === ListType.GridHorList;
        this.verticalList!.node.active = this.mShowType === ListType.VerticalList;
        this.horizontalList!.node.active = this.mShowType === ListType.HorizontalList;
        this.flexibleList!.node.active = this.mShowType === ListType.Flexible;

        this.mCurVirtualView?.PreloadItems();
        this.mCurVirtualView?.EnableDebugMode();
    }

    private InitItem(node: Node, index: number, data: ItemData) {
        const item = node.getComponent(VirtualViewItem);
        if (item) {
            const itemData = new VirtualViewItemData(index, data.data, data.type);
            item.Init(itemData);
        }
    }

    private UpdateItem(node: Node, index: number, data: ItemData) {
        const item = node.getComponent(VirtualViewItem);
        if (item) {
            const itemData = new VirtualViewItemData(index, data.data, data.type);
            item.Refresh(itemData);
        }
    }

    private OnScrolling(progress: number) {
        // 可以在这里处理滚动事件
    }

    private SwitchShowList(type: ListType) {
        this.mShowType = type;
        this.UpdateListShow();
        this.OnReset();
    }

    public OnShowVerticalList() {
        this.SwitchShowList(ListType.VerticalList);
    }

    public OnShowHorizontalList() {
        this.SwitchShowList(ListType.HorizontalList);
    }

    public OnShowGridVerList() {
        this.SwitchShowList(ListType.GridVerList);
    }

    public OnShowGridHorList() {
        this.SwitchShowList(ListType.GridHorList);
    }

    public OnShowFlexibleList() {
        this.SwitchShowList(ListType.Flexible);
    }

    public OnUpdateVirtualListFunc() {
        this.mCurVirtualView?.Refresh();
    }

    public OnUpdateIndexFunc() {
        this.mTestData[88].data.info = "已更新内容";
        this.mCurVirtualView?.ScrollToIndex(88, 0);
        this.mCurVirtualView?.UpdateItemAt(88, this.mTestData[88]);
    }

    public OnUpdateIndexSizeFunc() {
        this.mCurVirtualView?.UpdateItemSize(30, 400);
        this.mCurVirtualView?.ScrollToIndex(30);
    }

    public OnScrollTo20IndexFunc() {
        this.mCurVirtualView?.ScrollToIndex(20);
    }

    public OnMoveTo60IndexFunc() {
        this.mCurVirtualView?.ScrollToIndex(60, 0);
    }

    public OnMoveToTopFunc() {
        this.mCurVirtualView?.ScrollToIndex(0, 0);
    }

    public OnMoveToBottomFunc() {
        this.mCurVirtualView?.ScrollToIndex(this.mTestData.length - 1, 0);
    }

    public OnChangeTypeFunc() {
        const item = this.mTestData[50];
        item.type = ItemType.Flexible2;
        item.data.info = "已更改类型 tmp2";
        this.mCurVirtualView?.ScrollToIndex(50, 0);
        this.mCurVirtualView?.UpdateItemAt(50, item);
    }

    public OnInsertTo2IndexFunc() {
        const item: ItemData = {
            index: 2,
            type: ItemType.Flexible3,
            data: {
                info: "插入的内容",
                avatar: "item_1"
            }
        };
        this.mCurVirtualView?.InsertItemAt(2, item);
    }

    public OnRemoveTo3IndexFunc() {
        this.mCurVirtualView?.RemoveItemAt(3, true);
    }

    public OnReset() {
        this.InitTestData();
        this.mCurVirtualView?.ReloadData(this.mTestData);
        this.mCurVirtualView?.ScrollToIndex(0, 0);
    }
} 