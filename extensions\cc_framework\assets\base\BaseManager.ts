import { _decorator, Component, js } from 'cc';
import { Debug, ILog } from '../core/logger/Debug';
const { ccclass, property } = _decorator;

/**
 * 管理器基类
 * @description 所有游戏管理器的基类，提供基础的日志打印功能
 * 支持开发环境和生产环境下的不同日志格式
 * 开发环境下使用带颜色的格式化日志，生产环境使用普通日志
 */
@ccclass('BaseManager')
export class BaseManager extends Component {

    private _base_manager_name: string = js.getClassName(this);
    /** 管理器名称，自动获取类名 */
    public get managerName(): string { return this._base_manager_name; }

    /** 
     * 日志打印方法
     * @description 开发环境下使用带颜色的格式化日志，生产环境使用普通日志
     * 日志格式：[管理器名] LOG [时间戳]
     */
    protected get log(): ILog { return Debug.create('log', '#4169e1', this._base_manager_name); }

    /** 
     * 警告打印方法
     * @description 开发环境下使用带颜色的格式化警告，生产环境使用普通警告
     * 警告格式：[管理器名] WARN [时间戳]
     */
    protected get warn(): ILog { return Debug.create('warn', '#ff7f50', this._base_manager_name); }

    /** 
     * 错误打印方法
     * @description 开发环境下使用带颜色的格式化错误，生产环境使用普通错误
     * 错误格式：[管理器名] ERROR [时间戳]
     */
    protected get error(): ILog { return Debug.create('error', '#ff4757', this._base_manager_name); }
}