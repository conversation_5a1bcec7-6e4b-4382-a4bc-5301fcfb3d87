import { _decorator, Component, Enum, Node, Prefab, tween, UIOpacity, Vec3 } from 'cc';
import { PoolManager } from './PoolManager';
const { ccclass, property } = _decorator;

/**
 * 缓动类型枚举
 */
enum EasingType {
    linear = 'linear',
    smooth = 'smooth',
    fade = 'fade',
    quadIn = 'quadIn',
    quadOut = 'quadOut',
    quadInOut = 'quadInOut',
    quadOutIn = 'quadOutIn',
    cubicIn = 'cubicIn',
    cubicOut = 'cubicOut',
    cubicInOut = 'cubicInOut',
    cubicOutIn = 'cubicOutIn',
    quartIn = 'quartIn',
    quartOut = 'quartOut',
    quartInOut = 'quartInOut',
    quartOutIn = 'quartOutIn',
    quintIn = 'quintIn',
    quintOut = 'quintOut',
    quintInOut = 'quintInOut',
    quintOutIn = 'quintOutIn',
    sineIn = 'sineIn',
    sineOut = 'sineOut',
    sineInOut = 'sineInOut',
    sineOutIn = 'sineOutIn',
    expoIn = 'expoIn',
    expoOut = 'expoOut',
    expoInOut = 'expoInOut',
    expoOutIn = 'expoOutIn',
    circIn = 'circIn',
    circOut = 'circOut',
    circInOut = 'circInOut',
    circOutIn = 'circOutIn',
    elasticIn = 'elasticIn',
    elasticOut = 'elasticOut',
    elasticInOut = 'elasticInOut',
    elasticOutIn = 'elasticOutIn',
    backIn = 'backIn',
    backOut = 'backOut',
    backInOut = 'backInOut',
    backOutIn = 'backOutIn',
    bounceIn = 'bounceIn',
    bounceOut = 'bounceOut',
    bounceInOut = 'bounceInOut',
    bounceOutIn = 'bounceOutIn'
}

/**
 * 拖影效果组件
 * 用于创建节点的拖影效果，支持自定义缓动动画和透明度渐变
 */
@ccclass('ShadowEffect')
export class ShadowEffect extends Component {
    /**
     * 生成间隔
     */
    @property({
        displayName: '生成间隔'
    })
    public trailInterval: number = 0.01;

    /**
     * 持续时间
     */
    @property({
        displayName: '持续时间'
    })
    public trailDuration: number = 0.5;

    /**
     * 起始缩放比率
     */
    @property({
        displayName: '起始缩放比率'
    })
    public trailStartScale: number = 1;

    /**
     * 终止缩放比率
     */
    @property({
        displayName: '终止缩放比率'
    })
    public trailEndScale: number = 0.8;

    /**
     * 起始透明度
     */
    @property({
        displayName: '起始透明度'
    })
    public trailStartOpacity: number = 200;

    /**
     * 终止透明度
     */
    @property({
        displayName: '终止透明度'
    })
    public trailEndOpacity: number = 0;

    /**
     * 拖影样式预制体
     */
    @property({
        type: Prefab,
        displayName: '拖影样式'
    })
    public shadowPrefab: Prefab | null = null;

    /**
     * 拖影展示位置
     */
    @property({
        type: Node,
        displayName: '拖影展示位置'
    })
    public shadowPosition: Node | null = null;

    /**
     * 拖影缓动动画
     */
    @property({
        type: Enum(EasingType),
        displayName: '拖影缓动动画'
    })
    public easing: EasingType = EasingType.fade;

    /**
     * 拖影的父节点
     */
    @property({
        type: Node,
        tooltip: '(可选)拖影的父节点,用于node整洁管理',
        displayName: '(可选)拖影的父节点'
    })
    public ndShadowParent: Node | null = null;

    /**
     * 拖影计时器
     */
    private _trailTimer: number = 0;

    /**
     * 拖影节点列表
     */
    private _trailNodes: Node[] = [];

    /**
     * 节点名称
     */
    private nodeName: string = '';

    /**
     * 第一个节点
     */
    private firstNode: Node | null = null;

    /**
     * 组件启用时调用
     */
    protected onEnable(): void {
        this.createShadowParentFirstNode();
        this.nodeName = this.node.name;
    }

    /**
     * 组件禁用时调用
     */
    protected onDisable(): void {
        this._trailNodes.forEach(node => node.destroy());
    }

    /**
     * 创建拖影父节点的第一个节点
     */
    private createShadowParentFirstNode(): void {
        if (this.ndShadowParent) {
            if (this.ndShadowParent.children.length === 0) {
                this.firstNode = new Node('firstNode');
                this.firstNode.setParent(this.ndShadowParent);
            } else {
                this.firstNode = this.ndShadowParent.getChildByName('firstNode');
            }
        }
    }

    /**
     * 每帧更新
     * @param deltaTime 帧间隔时间
     */
    protected update(deltaTime: number): void {
        this._trailTimer += deltaTime;
        if (this._trailTimer >= this.trailInterval) {
            this._trailTimer = 0;
            this.createTrail();
        }
        this.cleanupTrails();
    }

    /**
     * 添加节点到父节点
     * @param node 要添加的节点
     */
    private addToParent(node: Node): void {
        if (this.ndShadowParent) {
            this.ndShadowParent.addChild(node);
            if (this.firstNode) {
                node.setSiblingIndex(this.firstNode.getSiblingIndex());
            }
            this._trailNodes.push(node);
        } else if (this.node.parent) {
            this.node.parent.addChild(node);
            node.setSiblingIndex(this.node.getSiblingIndex());
            this._trailNodes.push(node);
        }
    }

    /**
     * 获取透明度组件
     * @param node 目标节点
     * @returns UIOpacity组件
     */
    private getOpacityComponent(node: Node): UIOpacity {
        let opacity = node.getComponent(UIOpacity);
        if (!opacity) {
            opacity = node.addComponent(UIOpacity);
        }
        return opacity;
    }

    /**
     * 创建拖影
     */
    private createTrail(): void {
        if (!this.shadowPrefab || !this.shadowPosition) return;

        const trailNode = PoolManager.instance.get(this.shadowPrefab);
        trailNode.name = this.nodeName + '_trail';
        this.addToParent(trailNode);

        // 设置位置和角度
        trailNode.worldPosition = this.shadowPosition.worldPosition;
        trailNode.angle = this.node.angle;

        // 设置初始状态
        const opacity = this.getOpacityComponent(trailNode);
        opacity.opacity = this.trailStartOpacity;
        trailNode.scale = new Vec3(this.trailStartScale, this.trailStartScale, 0);

        // 创建缩放动画
        tween(trailNode)
            .to(this.trailDuration, {
                scale: new Vec3(this.trailEndScale, this.trailEndScale, 0)
            }, {
                easing: this.easing
            })
            .start();

        // 创建透明度动画
        tween(opacity)
            .to(this.trailDuration, {
                opacity: this.trailEndOpacity
            }, {
                easing: this.easing
            })
            .call(() => {
                const index = this._trailNodes.indexOf(trailNode);
                if (index !== -1) {
                    this._trailNodes.splice(index, 1);
                }
                PoolManager.instance.put(trailNode);
            })
            .start();
    }

    /**
     * 清理无效的拖影节点
     */
    private cleanupTrails(): void {
        this._trailNodes = this._trailNodes.filter(node => node.isValid);
    }
} 