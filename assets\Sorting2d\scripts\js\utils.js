System.register("chunks:///_virtual/utils.ts", ["cc"], (function (t) {
    var e, n;
    return {
        setters: [function (t) {
            e = t.cclegacy,
                n = t.Vec3
        }
        ],
        execute: function () {
            t("signSqrtDistToPlane", (function (t, e, u) {
                return n.subtract(c, u, e),
                    c.dot(t)
            }
            )),
                e._RF.push({}, "4148ebHeEJFO6jUFGWcnMu2", "utils", void 0);
            var c = new n;
            e._RF.pop()
        }
    }
}
));