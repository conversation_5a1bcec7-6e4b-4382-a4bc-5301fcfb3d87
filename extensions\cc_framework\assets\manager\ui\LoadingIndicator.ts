import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\core\manager\ui\LoadingIndicator.ts
 * @ author: OldPoint
 * @ data: 2025-03-15 19:42
 * @ description: 加载指示器组件 - 用于显示游戏加载过程中的动画效果
 * 提供旋转动画效果，可自定义旋转速度和方向
 */
@ccclass('LoadingIndicator')
export class LoadingIndicator extends Component {

    /** 加载动画节点 - 用于显示旋转的加载图标 */
    @property(Node)
    private loading: Node = null!;

    /** 当前旋转角度 - 用于计算旋转动画 */
    private loading_rotate: number = 0;

    /**
     * 更新加载动画旋转
     * @param dt 帧间隔时间（秒）
     * @description 每帧更新加载图标的旋转角度，实现平滑的旋转动画效果
     * 旋转速度为220度/秒，当角度超过360度时重置
     */
    protected update(dt: number): void {
        // 计算新的旋转角度
        this.loading_rotate += dt * 220;
        // 应用旋转到加载图标
        this.loading.setRotationFromEuler(0, 0, -this.loading_rotate % 360)
        // 重置角度，避免数值过大
        if (this.loading_rotate > 360) {
            this.loading_rotate -= 360;
        }
    }

}