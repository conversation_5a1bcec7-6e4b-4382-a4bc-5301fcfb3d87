/**
 * 集合类
 * <AUTHOR>
 * @data 2025-03-14 11:36
 * @filePath assets\core\lib\collection\Collection.ts
 * @description 扩展自Map的集合类，提供键值对存储和数组访问功能
 * 特点：
 * 1. 继承Map的所有功能
 * 2. 维护一个内部数组，支持按插入顺序访问元素
 * 3. 提供数组形式的访问接口
 * @template K 键的类型
 * @template V 值的类型
 * @example
 * const collection = new Collection<string, number>();
 * collection.set("key1", 1);
 * collection.set("key2", 2);
 * console.log(collection.array); // [1, 2]
 */
export class Collection<K, V> extends Map<K, V> {

    /** 内部数组，用于按插入顺序存储值 */
    private _array: V[] = [];

    /** 
     * 获取数组对象
     * @returns 包含所有值的数组，按插入顺序排列
     * @example
     * const values = collection.array;
     */
    public get array() {
        return this._array;
    }

    /**
     * 设置键值对
     * @description 如果键已存在，则更新对应的值；如果键不存在，则添加新的键值对
     * @param key 键
     * @param value 值
     * @returns this 支持链式调用
     * @example
     * collection.set("key", value);
     */
    public set(key: K, value: V): this {
        if (this.has(key)) {
            var old = this.get(key)!;
            var index = this._array.indexOf(old);
            this._array[index] = value;
        } else {
            this._array.push(value);
        }
        return super.set(key, value);
    }

    /**
     * 删除指定键的值
     * @description 从集合和内部数组中删除指定键的值
     * @param key 要删除的键
     * @returns 是否成功删除
     * @example
     * const deleted = collection.delete("key");
     */
    public delete(key: K): boolean {
        const value = this.get(key);
        if (value) {
            const index = this._array.indexOf(value);
            if (index > -1) this._array.splice(index, 1);
            return super.delete(key);
        }
        return false;
    }

    /**
     * 清空集合
     * @description 清空所有键值对和内部数组
     * @example
     * collection.clear();
     */
    public clear(): void {
        this._array.splice(0, this._array.length);
        super.clear();
    }
}