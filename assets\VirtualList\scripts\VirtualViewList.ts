import { _decorator, CCFloat, CCInteger, Component, Enum, instantiate, isValid, Node, NodePool, ScrollView, Size, UITransform, Vec2, Vec3, Widget } from 'cc';
const { ccclass, property, menu, requireComponent } = _decorator;

/**
 * 列表布局类型枚举
 */
enum ViewLayoutType {
    VERTICAL = 0,    // 垂直列表
    HORIZONTAL = 1,  // 水平列表
    GRID = 2        // 网格列表
}

/**
 * 滚动方向枚举
 */
enum ScrollDirection {
    VERTICAL = 0,    // 垂直滚动
    HORIZONTAL = 1   // 水平滚动
}

/**
 * 列表项数据接口
 */
export interface ItemData {
    index: number;   // 项目索引
    type: string;    // 项目类型
    data: {          // 项目数据
        info?: string;
        icon?: string;
        avatar?: string;
        name?: string;
        [key: string]: any;
    };
}

/**
 * 模板项接口
 */
interface TemplateItem {
    type: string;                    // 模板类型
    node: Node | (() => Node);       // 模板节点或创建函数
    pool: NodePool;                  // 对象池
}

/**
 * 可见项索引范围接口
 */
interface VisibleIndices {
    start: number;   // 起始索引
    end: number;     // 结束索引
}

/**
 * 渲染统计信息接口
 */
interface RenderStats {
    frameTime: number;       // 帧渲染时间
    visibleCount: number;    // 可见项数量
    recycleCount: number;    // 回收项数量
    reusedCount: number;     // 重用项数量
}

/**
 * 查找缓存接口
 */
interface FindCache {
    offset: number;  // 偏移量
    index: number;   // 索引
    size: number;    // 大小
}

/**
 * 加载参数接口
 */
interface LoadParameters {
    bufferCount: number;     // 缓冲区数量
    batchSize: number;       // 批处理大小
}

/**
 * 回调函数接口
 */
export interface Callbacks {
    onItemInit: (node: Node, index: number, data: ItemData) => void;      // 项目初始化回调
    onItemUpdate: (node: Node, index: number, data: ItemData) => void;    // 项目更新回调
    onScrolling: (progress: number) => void;                              // 滚动进度回调
}

// 为Node添加重用标记
declare module 'cc' {
    interface Node {
        __reused__?: boolean;
    }
}

/**
 * 虚拟列表组件类
 * 支持垂直、水平和网格布局
 */
/**
 * 虚拟列表组件
 * 用于高效显示大量数据的列表，通过只渲染可见区域的内容来优化性能
 */
@ccclass('VirtualViewList')
@menu('自定义工具/虚拟列表')
@requireComponent(ScrollView)
export class VirtualViewList extends Component {
    @property
    private _scrollView: ScrollView = null!;
    @property({ type: ScrollView, displayName: "滚动视图" })
    public get scrollView(): ScrollView {
        if (!this._scrollView) {
            const view = this.node.getComponent(ScrollView);
            if (view) this._scrollView = view;
        }
        return this._scrollView;
    }
    public set scrollView(value: ScrollView) {
        this._scrollView = value;
    }
    @property
    private _layoutType: ViewLayoutType = ViewLayoutType.VERTICAL;
    @property({ type: Enum(ViewLayoutType), displayName: "列表布局", tooltip: "垂直布局: VERTICAL\n水平布局: HORIZONTAL\n网格布局: GRID" })
    public get layoutType(): ViewLayoutType {
        return this._layoutType;
    }
    public set layoutType(value: ViewLayoutType) {
        this._layoutType = value;
        if (this.scrollView) this.InitScrollView();
    }
    @property({ displayName: "单元间距", visible: function (this: VirtualViewList) { return this.layoutType !== ViewLayoutType.GRID; } })
    private itemSpacing: number = 10;
    @property
    private _scrollDirection: ScrollDirection = ScrollDirection.VERTICAL;
    @property({
        type: Enum(ScrollDirection), displayName: "主轴滚动方向", tooltip: "垂直滚动: VERTICAL\n水平滚动: HORIZONTAL",
        visible: function (this: VirtualViewList) { return this.layoutType === ViewLayoutType.GRID; }
    })
    public get scrollDirection(): ScrollDirection {
        return this._scrollDirection;
    }
    public set scrollDirection(value: ScrollDirection) {
        this._scrollDirection = value;
        if (this.scrollView) this.InitScrollView();
    }

    @property({
        type: CCInteger, min: 1, displayName: "列数", tooltip: "垂直滚动: 列数",
        visible: function (this: VirtualViewList) { return this.layoutType === ViewLayoutType.GRID && this.scrollDirection === ScrollDirection.VERTICAL; }
    })
    private cols: number = 2;

    @property({
        type: CCFloat, tooltip: "垂直滚动: item行间距", displayName: "单元行间距",
        visible: function (this: VirtualViewList) {
            return this.layoutType === ViewLayoutType.GRID && this.scrollDirection === ScrollDirection.VERTICAL;
        }
    })
    private girdVertRowsSpacing: number = 10;

    @property({
        type: CCFloat,
        tooltip: "垂直滚动: item列间距", displayName: "单元列间距",
        visible: function (this: VirtualViewList) {
            return this.layoutType === ViewLayoutType.GRID && this.scrollDirection === ScrollDirection.VERTICAL;
        }
    })
    private girdVertColsSpacing: number = 10;

    @property({
        type: CCInteger, min: 1, displayName: "行数", tooltip: "水平滚动：行数",
        visible: function (this: VirtualViewList) {
            return this.layoutType === ViewLayoutType.GRID && this.scrollDirection === ScrollDirection.HORIZONTAL;
        }
    })
    private rows: number = 2;

    @property({
        type: CCFloat, displayName: "单元列间距", tooltip: "水平滚动: item列间距",
        visible: function (this: VirtualViewList) {
            return this.layoutType === ViewLayoutType.GRID && this.scrollDirection === ScrollDirection.HORIZONTAL;
        }
    })
    private girdHorizontalColsSpacing: number = 10;

    @property({
        type: CCFloat, displayName: "单元行间距", tooltip: "水平滚动: item行间距",
        visible: function (this: VirtualViewList) {
            return this.layoutType === ViewLayoutType.GRID && this.scrollDirection === ScrollDirection.HORIZONTAL;
        }
    })
    private girdHorizontalRowsSpacing: number = 10;

    @property({ displayName: "自动调整刷新频率", tooltip: "自动调整刷新频率以优化性能" })
    private autoOptimizePerformance: boolean = true;

    @property({ type: CCFloat, range: [0, 1], slide: true, step: 0.1, displayName:"预加载缓冲区大小", tooltip: "0-1 表示可见区域的倍数" })
    private cacheRatio: number = 0.5;

    // Private properties
    private mItemSizes: number[] = [];
    private mCumulativeSizes: number[] = [];
    private mContent: Node = null!;
    private mVisibleItemsMap: Map<number, Node> = new Map();
    private mLoadingQueue: number[] = [];
    private mLastFindCache: FindCache | null = null;
    private mTemplateItems: Map<string, TemplateItem> = new Map();
    private mDefaultTemplateType: string = "default";
    private mItemsData: ItemData[] = [];
    private mLoadingScheduled: boolean = false;
    private mCellWidth: number = 0;
    private mCellHeight: number = 0;
    private mNeedFrameLoading: boolean = false;
    private mIsDebugMode: boolean = false;
    private mRenderStats: RenderStats = {
        frameTime: 0,
        visibleCount: 0,
        recycleCount: 0,
        reusedCount: 0
    };
    private mLastVisibleIndices: VisibleIndices = { start: 0, end: 0 };
    private mItemInitCallback: ((node: Node, index: number, data: ItemData) => void) | null = null;
    private mItemUpdateCallback: ((node: Node, index: number, data: ItemData) => void) | null = null;
    private mScrollCallback: ((progress: number) => void) | null = null;
    private mLastUpdateTime: number = 0;
    private mUpdateInterval: number = 16;
    private mScrollVelocity: number = 0;
    private mLastScrollPos: number = 0;
    private mFrameCount: number = 0;
    private mForceUpdate: boolean = false;
    private mTotalItemCount: number = 0;
    private mAutoPreloadCount: number = 10;

    /**
     * 组件加载时初始化
     */
    onLoad() {
        this.InitScrollView();
        this.mContent = this.scrollView.content!;
        this.mContent.getComponent(UITransform)!.anchorPoint = this.scrollView.vertical ? new Vec2(0.5, 1) : new Vec2(0, 0.5);
    }

    /**
     * 组件销毁时清理资源
     */
    onDestroy() {
        this.unscheduleAllCallbacks();
        this.ClearTemplates();
    }

    /**
     * 组件启用时注册事件
     */
    onEnable() {
        this.scrollView.node.on(ScrollView.EventType.SCROLLING, this.OnScrolling, this);
        this.scrollView.node.on(ScrollView.EventType.SCROLL_ENDED, this.OnScrollEnded, this);
    }

    /**
     * 组件禁用时注销事件
     */
    onDisable() {
        this.scrollView.node.off(ScrollView.EventType.SCROLLING, this.OnScrolling, this);
        this.scrollView.node.off(ScrollView.EventType.SCROLL_ENDED, this.OnScrollEnded, this);
    }

    /**
     * 初始化滚动视图
     * 根据布局类型设置滚动方向
     */
    private InitScrollView() {
        this.scrollView.vertical = false;
        this.scrollView.horizontal = false;

        switch (this.layoutType) {
            case ViewLayoutType.VERTICAL:
                this.scrollView.vertical = true;
                break;
            case ViewLayoutType.HORIZONTAL:
                this.scrollView.horizontal = true;
                break;
            case ViewLayoutType.GRID:
                switch (this.scrollDirection) {
                    case ScrollDirection.VERTICAL:
                        this.scrollView.vertical = true;
                        break;
                    case ScrollDirection.HORIZONTAL:
                        this.scrollView.horizontal = true;
                        break;
                }
                break;
        }
    }

    /**
     * 注册单个模板
     * @param type 模板类型
     * @param node 模板节点或创建函数
     * @param isDefault 是否为默认模板
     */
    public RegisterTemplate(type: string, node: Node | (() => Node), isDefault: boolean = false) {
        const pool = new NodePool();
        this.mTemplateItems.set(type, { type, node, pool });
        if (isDefault) {
            this.mDefaultTemplateType = type;
        }
    }

    /**
     * 注册多个模板
     * @param templates 模板数组
     * @param defaultType 默认模板类型
     */
    public RegisterTemplates(templates: { type: string, node: Node | (() => Node) }[], defaultType?: string) {
        for (const template of templates) {
            this.RegisterTemplate(template.type, template.node);
        }
        if (defaultType && this.mTemplateItems.has(defaultType)) {
            this.mDefaultTemplateType = defaultType;
        }
    }

    /**
     * 清理所有模板
     */
    public ClearTemplates() {
        this.mTemplateItems.forEach(item => {
            if (item.pool) {
                item.pool.clear();
            }
        });
        this.mTemplateItems.clear();
        this.mDefaultTemplateType = "default";
    }

    /**
     * 设置回调函数
     * @param callbacks 回调函数对象
     */
    public SetCallbacks(callbacks: Callbacks) {
        if (callbacks.onItemInit) {
            this.mItemInitCallback = callbacks.onItemInit;
        }
        if (callbacks.onItemUpdate) {
            this.mItemUpdateCallback = callbacks.onItemUpdate;
        }
        if (callbacks.onScrolling) {
            this.mScrollCallback = callbacks.onScrolling;
        }
    }

    /**
     * 获取模板节点
     * @param index 项目索引
     * @returns 模板节点
     */
    private GetTemplateNode(index: number): Node {
        const itemData = this.mItemsData[index];
        if (!itemData) {
            throw new Error(`VirtualViewList: No data for index ${index}`);
        }

        const type = itemData.type || this.mDefaultTemplateType;
        if (!type) {
            throw new Error(`VirtualViewList: No type specified for item at index ${index}`);
        }

        const template = this.mTemplateItems.get(type) || this.mTemplateItems.get(this.mDefaultTemplateType);
        if (!template) {
            throw new Error(`VirtualViewList: No template found for type ${type}`);
        }

        let node: Node | null = template.pool.get();
        if (!node) {
            if (template.node instanceof Node) {
                node = instantiate(template.node);
            } else if (typeof template.node === 'function') {
                node = template.node();
            }
        }

        if (!node || !isValid(node)) {
            throw new Error(`VirtualViewList: Failed to create node for type ${type}`);
        }

        return node as Node;
    }

    /**
     * 重置所有状态
     */
    private ResetAllStates() {
        this.mLastVisibleIndices = { start: -1, end: -1 };
        this.mLastFindCache = null;
        this.mLastUpdateTime = 0;
        this.mFrameCount = 0;
        this.mScrollVelocity = 0;
        this.mLastScrollPos = 0;
        this.mLoadingQueue = [];
        this.mLoadingScheduled = false;
        this.mRenderStats = {
            frameTime: 0,
            visibleCount: 0,
            recycleCount: 0,
            reusedCount: 0
        };
    }

    /**
     * 重新加载数据
     * @param data 数据数组
     */
    public ReloadData(data: ItemData[]) {
        this.mItemsData = data.slice();
        this.mTotalItemCount = this.mItemsData.length;
        this.mNeedFrameLoading = true;

        if (data.length !== 0) {
            this.ResetAllStates();
            this.InitializeItemSizes();
            this.UpdateCumulativeSizes();
            this.UpdateContentSize();
            this.ClearVisibleItems();
            this.UpdateList();
        } else {
            this.HandleEmptyData();
        }
    }

    /**
     * 初始化项目大小
     */
    private InitializeItemSizes() {
        this.mItemSizes = [];
        for (let i = 0; i < this.mItemsData.length; i++) {
            const itemData = this.mItemsData[i];
            const type = itemData?.type || this.mDefaultTemplateType;
            let node: Node | null = null;
            const template = this.mTemplateItems.get(type) || this.mTemplateItems.get(this.mDefaultTemplateType);

            if (template) {
                if (template.node instanceof Node) {
                    node = template.node;
                } else if (typeof template.node === 'function') {
                    node = template.node();
                    if (template.pool) {
                        template.pool.put(node);
                    }
                }
            }

            if (node) {
                const transform = node.getComponent(UITransform);
                if (transform) {
                    const size = this.scrollView.vertical ? transform.height : transform.width;
                    this.mItemSizes.push(size);
                    if (i === 0 && this.layoutType === ViewLayoutType.GRID) {
                        this.mCellWidth = transform.width;
                        this.mCellHeight = transform.height;
                    }
                } else {
                    console.error('VirtualViewList: 模板节点缺少UITransform组件');
                    this.mItemSizes.push(50);
                }
            } else {
                console.error(`VirtualViewList: 无法获取索引 ${i} (类型:${type}) 的模板节点`);
                this.mItemSizes.push(50);
            }
        }
    }

    /**
     * 处理空数据情况
     */
    private HandleEmptyData() {
        this.ClearVisibleItems();
        const contentTransform = this.mContent.getComponent(UITransform)!;
        const viewTransform = this.scrollView.node.getComponent(UITransform)!.contentSize;
        contentTransform.contentSize = viewTransform;
    }

    /**
     * 清理可见项
     */
    private ClearVisibleItems() {
        this.mVisibleItemsMap.forEach((node, index) => {
            this.RecycleNode(node, index);
        });
        this.mVisibleItemsMap.clear();
    }

    /**
     * 回收节点
     * @param node 要回收的节点
     * @param index 节点索引
     */
    private RecycleNode(node: Node, index: number) {
        if (node && isValid(node, true)) {
            node.__reused__ = true;
            const itemData = this.mItemsData[index];
            const type = itemData?.type || this.mDefaultTemplateType;
            const template = this.mTemplateItems.get(type) || this.mTemplateItems.get(this.mDefaultTemplateType);
            if (template && template.pool) {
                template.pool.put(node);
            }
        }
    }

    /**
     * 刷新列表
     */
    public Refresh() {
        if (this.mTotalItemCount !== 0) {
            this.mForceUpdate = true;
            this.mNeedFrameLoading = false;
            this.UpdateCumulativeSizes();
            this.UpdateContentSize();
            this.ClearVisibleItems();
            this.UpdateList();
        } else {
            this.HandleEmptyData();
        }
    }

    /**
     * 滚动到顶部
     * @param duration 滚动持续时间
     * @param callback 完成回调
     */
    public ScrollToTop(duration: number = 0.3, callback?: () => void) {
        this.ScrollToIndex(0, duration, callback);
    }

    /**
     * 滚动到底部
     * @param duration 滚动持续时间
     * @param callback 完成回调
     */
    public ScrollToBottom(duration: number = 0.3, callback?: () => void) {
        this.ScrollToIndex(this.mTotalItemCount - 1, duration, callback);
    }

    /**
     * 滚动到指定索引
     * @param index 目标索引
     * @param duration 滚动持续时间
     * @param callback 完成回调
     */
    public ScrollToIndex(index: number, duration: number = 0.3, callback?: () => void) {
        if (index < 0 || index > this.mTotalItemCount - 1) {
            return;
        }

        const isVertical = this.scrollView.vertical;
        const contentSize = this.mContent.getComponent(UITransform)!.contentSize;
        const viewSize = this.scrollView.node.getComponent(UITransform)!.contentSize;
        const viewLength = isVertical ? viewSize.height : viewSize.width;
        const contentLength = isVertical ? contentSize.height : contentSize.width;

        let targetOffset = 0;
        if (this.layoutType === ViewLayoutType.GRID) {
            if (this.scrollDirection === ScrollDirection.VERTICAL) {
                targetOffset = Math.floor(index / this.cols) * (this.mCellHeight + this.girdVertRowsSpacing) + this.mCellHeight / 2;
            } else {
                targetOffset = Math.floor(index / this.rows) * (this.mCellWidth + this.girdHorizontalColsSpacing) + this.mCellWidth / 2;
            }
        } else {
            targetOffset = index === 0 ? this.mItemSizes[0] / 2 : this.mCumulativeSizes[index - 1] + this.mItemSizes[index] / 2;
        }

        let scrollOffset = targetOffset - viewLength / 2;
        if (index === this.mItemSizes.length - 1) {
            scrollOffset = Math.min(scrollOffset, contentLength - viewLength / 2);
        }
        scrollOffset = Math.max(0, scrollOffset);

        this.scrollView.stopAutoScroll();
        if (isVertical) {
            this.scrollView.scrollToOffset(new Vec2(0, scrollOffset), duration);
        } else {
            this.scrollView.scrollToOffset(new Vec2(scrollOffset, 0), duration);
        }

        if (duration === 0) {
            this.mNeedFrameLoading = false;
            this.UpdateList();
            callback?.();
        } else if (callback) {
            this.scheduleOnce(() => callback(), duration);
        }
    }

    /**
     * 获取指定索引的节点
     * @param index 项目索引
     * @returns 节点对象
     */
    public GetItemNode(index: number): Node | null {
        return this.mVisibleItemsMap.get(index) || null;
    }

    /**
     * 更新指定索引的项目
     * @param index 项目索引
     * @param data 新数据
     */
    public UpdateItemAt(index: number, data?: ItemData) {
        if (index < 0 || index >= this.mItemSizes.length) {
            return;
        }

        const node = this.GetItemNode(index);
        if (node && this.mItemUpdateCallback) {
            if (data) {
                this.mItemsData[index] = data;
            }
            const itemData = this.mItemsData[index];
            this.mItemUpdateCallback(node, index, itemData);
        }
    }

    /**
     * 更新项目大小
     * @param index 项目索引
     * @param size 新大小
     */
    public UpdateItemSize(index: number, size: number) {
        if (this.layoutType === ViewLayoutType.GRID) {
            console.warn('VirtualViewList: UpdateItemSize不支持网格布局');
            return;
        }

        if (index < 0 || index >= this.mItemSizes.length) {
            return;
        }

        this.mItemSizes[index] = size;
        this.mForceUpdate = true;
        this.UpdateCumulativeSizes();
        this.UpdateContentSize();
        this.ClearVisibleItems();
        this.UpdateList();
    }

    /**
     * 更新节点大小
     * @param node 目标节点
     * @param size 新大小
     */
    private UpdateNewSize(node: Node, size: number) {
        const transform = node.getComponent(UITransform);
        if (!transform) {
            return;
        }

        const widget = node.getComponent(Widget);
        if (widget) {
            widget.updateAlignment();
        }

        switch (this.layoutType) {
            case ViewLayoutType.VERTICAL:
                transform.setContentSize(new Size(transform.contentSize.width, size));
                break;
            case ViewLayoutType.HORIZONTAL:
                transform.setContentSize(new Size(size, transform.contentSize.height));
                break;
            case ViewLayoutType.GRID:
                transform.setContentSize(new Size(this.mCellWidth, this.mCellHeight));
                break;
        }
    }

    /**
     * 更新累积大小数组
     */
    private UpdateCumulativeSizes() {
        this.mCumulativeSizes = [];

        switch (this.layoutType) {
            case ViewLayoutType.VERTICAL:
            case ViewLayoutType.HORIZONTAL:
                let total = 0;
                for (let i = 0; i < this.mItemSizes.length; i++) {
                    total += this.mItemSizes[i] + this.itemSpacing;
                    this.mCumulativeSizes.push(total);
                }
                break;
            case ViewLayoutType.GRID:
                const itemCount = this.mItemSizes.length;
                switch (this.scrollDirection) {
                    case ScrollDirection.VERTICAL:
                        const rows = Math.ceil(itemCount / this.cols);
                        for (let row = 0; row < rows; row++) {
                            const rowHeight = (row + 1) * this.mCellHeight + row * this.girdVertRowsSpacing;
                            for (let col = 0; col < this.cols; col++) {
                                if (row * this.cols + col < itemCount) {
                                    this.mCumulativeSizes.push(rowHeight);
                                }
                            }
                        }
                        break;
                    case ScrollDirection.HORIZONTAL:
                        const cols = Math.ceil(itemCount / this.rows);
                        for (let col = 0; col < cols; col++) {
                            const colWidth = (col + 1) * this.mCellWidth + col * this.girdHorizontalColsSpacing;
                            for (let row = 0; row < this.rows; row++) {
                                if (col * this.rows + row < itemCount) {
                                    this.mCumulativeSizes.push(colWidth);
                                }
                            }
                        }
                        break;
                }
                break;
        }
    }

    /**
     * 更新内容大小
     */
    private UpdateContentSize() {
        let contentHeight = 0;
        let contentWidth = 0;
        const contentTransform = this.mContent.getComponent(UITransform)!;
        const viewSize = this.scrollView.node.getComponent(UITransform)!.contentSize;

        switch (this.layoutType) {
            case ViewLayoutType.VERTICAL:
                contentHeight = this.mCumulativeSizes.length > 0 ?
                    this.mCumulativeSizes[this.mCumulativeSizes.length - 1] : 0;
                contentHeight = Math.max(contentHeight, viewSize.height);
                contentTransform.contentSize = new Size(contentTransform.width, contentHeight);
                break;
            case ViewLayoutType.HORIZONTAL:
                contentWidth = this.mCumulativeSizes.length > 0 ?
                    this.mCumulativeSizes[this.mCumulativeSizes.length - 1] : 0;
                contentWidth = Math.max(contentWidth, viewSize.width);
                contentTransform.contentSize = new Size(contentWidth, contentTransform.height);
                break;
            case ViewLayoutType.GRID:
                switch (this.scrollDirection) {
                    case ScrollDirection.VERTICAL:
                        const rows = Math.ceil(this.mItemSizes.length / this.cols);
                        contentHeight = rows * this.mCellHeight + (rows - 1) * this.girdVertRowsSpacing;
                        contentHeight = Math.max(contentHeight, viewSize.height);
                        contentWidth = this.cols * this.mCellWidth + (this.cols - 1) * this.girdVertColsSpacing;
                        contentWidth = Math.max(contentWidth, viewSize.width);
                        break;
                    case ScrollDirection.HORIZONTAL:
                        const cols = Math.ceil(this.mItemSizes.length / this.rows);
                        contentWidth = cols * this.mCellWidth + (cols - 1) * this.girdHorizontalColsSpacing;
                        contentWidth = Math.max(contentWidth, viewSize.width);
                        contentHeight = this.rows * this.mCellHeight + (this.rows - 1) * this.girdHorizontalRowsSpacing;
                        contentHeight = Math.max(contentHeight, viewSize.height);
                        break;
                }
                contentTransform.contentSize = new Size(contentWidth, contentHeight);
                break;
        }
    }

    /**
     * 滚动事件处理
     */
    private OnScrolling() {
        const offset = this.scrollView.getScrollOffset();
        const scrollPos = this.scrollView.vertical ? offset.y : offset.x;

        this.mScrollVelocity = Math.abs(scrollPos - this.mLastScrollPos);
        this.mLastScrollPos = scrollPos;

        if (this.mScrollCallback) {
            const contentSize = this.mContent.getComponent(UITransform)!.contentSize;
            const viewSize = this.scrollView.node.getComponent(UITransform)!.contentSize;
            const maxScroll = this.scrollView.vertical ?
                contentSize.height - viewSize.height :
                contentSize.width - viewSize.width;

            if (maxScroll > 0) {
                const progress = Math.max(0, Math.min(1, scrollPos / maxScroll));
                this.mScrollCallback(progress);
            }
        }

        if (this.autoOptimizePerformance) {
            this.mFrameCount++;
            const now = Date.now();
            if (this.mScrollVelocity > 20) {
                this.mUpdateInterval = 8;
            } else if (this.mScrollVelocity > 10) {
                this.mUpdateInterval = 16;
            } else {
                this.mUpdateInterval = 33;
            }

            if (now - this.mLastUpdateTime > this.mUpdateInterval) {
                this.mNeedFrameLoading = false;
                this.UpdateList();
                this.mLastUpdateTime = now;
                this.mFrameCount = 0;
            }
        } else {
            const now = Date.now();
            if (now - this.mLastUpdateTime > this.mUpdateInterval) {
                this.mNeedFrameLoading = false;
                this.UpdateList();
                this.mLastUpdateTime = now;
            }
        }
    }

    /**
     * 滚动结束事件处理
     */
    private OnScrollEnded() {
        this.mNeedFrameLoading = false;
        this.UpdateList();
        this.mScrollVelocity = 0;
        this.mUpdateInterval = 16;
    }

    /**
     * 计算加载参数
     * @returns 加载参数对象
     */
    private ComputeLoadParameters(): LoadParameters {
        const isVertical = this.scrollView.vertical;
        const viewSize = this.scrollView.node.getComponent(UITransform)!.contentSize;
        const viewLength = isVertical ? viewSize.height : viewSize.width;

        if (this.mItemSizes.length === 0) {
            return { bufferCount: 1, batchSize: 1 };
        }

        const cacheRatio = Math.max(0.1, this.cacheRatio);

        switch (this.layoutType) {
            case ViewLayoutType.VERTICAL:
            case ViewLayoutType.HORIZONTAL:
                const avgItemSize = this.mItemSizes.reduce((sum, size) => sum + size, 0) / this.mItemSizes.length;
                const visibleCount = Math.ceil(viewLength / avgItemSize);
                return {
                    bufferCount: Math.max(1, Math.ceil(visibleCount * cacheRatio)),
                    batchSize: Math.max(1, Math.ceil(0.5 * visibleCount))
                };
            case ViewLayoutType.GRID:
                let cellSize = 0;
                let visibleCells = 0;
                switch (this.scrollDirection) {
                    case ScrollDirection.VERTICAL:
                        cellSize = this.mCellHeight + this.girdVertRowsSpacing;
                        visibleCells = Math.ceil(viewLength / cellSize);
                        return {
                            bufferCount: Math.max(1, Math.ceil(visibleCells * cacheRatio)),
                            batchSize: Math.max(1, Math.ceil(0.5 * visibleCells)) * this.cols
                        };
                    case ScrollDirection.HORIZONTAL:
                        cellSize = this.mCellWidth + this.girdHorizontalColsSpacing;
                        visibleCells = Math.ceil(viewLength / cellSize);
                        return {
                            bufferCount: Math.max(1, Math.ceil(visibleCells * cacheRatio)),
                            batchSize: Math.max(1, Math.ceil(0.5 * visibleCells)) * this.rows
                        };
                }
            default: return { bufferCount: 1, batchSize: 1 };
        }
    }

    /**
     * 更新列表
     */
    private UpdateList() {
        if (this.mItemSizes.length === 0) {
            return;
        }

        const startTime = this.mIsDebugMode ? Date.now() : 0;
        const isVertical = this.scrollView.vertical;
        const offset = this.scrollView.getScrollOffset();
        const viewSize = this.scrollView.node.getComponent(UITransform)!.contentSize;
        const viewLength = isVertical ? viewSize.height : viewSize.width;

        let startIndex = 0;
        let endIndex = 0;
        const loadParams = this.ComputeLoadParameters();

        switch (this.layoutType) {
            case ViewLayoutType.VERTICAL:
            case ViewLayoutType.HORIZONTAL:
                const scrollPos = isVertical ? offset.y : -offset.x;
                startIndex = this.FindIndex(scrollPos, true);
                startIndex = Math.max(0, startIndex - loadParams.bufferCount);
                endIndex = this.FindIndex(scrollPos + viewLength, false);
                endIndex = Math.min(this.mItemSizes.length, endIndex + loadParams.bufferCount);
                break;
            case ViewLayoutType.GRID:
                switch (this.scrollDirection) {
                    case ScrollDirection.VERTICAL:
                        const cellHeight = this.mCellHeight + this.girdVertRowsSpacing;
                        const startRow = Math.floor(Math.abs(offset.y) / cellHeight);
                        const visibleRows = Math.ceil(viewLength / cellHeight);
                        const totalRows = Math.ceil(this.mItemSizes.length / this.cols);
                        const endRow = Math.min(totalRows, startRow + visibleRows + 2 * loadParams.bufferCount);
                        startIndex = Math.max(0, startRow - loadParams.bufferCount) * this.cols;
                        endIndex = Math.min(this.mItemSizes.length, endRow * this.cols);
                        break;
                    case ScrollDirection.HORIZONTAL:
                        const cellWidth = this.mCellWidth + this.girdHorizontalColsSpacing;
                        const startCol = Math.floor(Math.abs(offset.x) / cellWidth);
                        const visibleCols = Math.ceil(viewLength / cellWidth);
                        const totalCols = Math.ceil(this.mItemSizes.length / this.rows);
                        const endCol = Math.min(totalCols, startCol + visibleCols + 2 * loadParams.bufferCount);
                        startIndex = Math.max(0, startCol - loadParams.bufferCount) * this.rows;
                        endIndex = Math.min(this.mItemSizes.length, endCol * this.rows);
                        break;
                }
                break;
        }

        if (this.mForceUpdate || startIndex !== this.mLastVisibleIndices.start ||
            endIndex !== this.mLastVisibleIndices.end || this.mLoadingQueue.length !== 0) {

            this.mLastVisibleIndices = { start: startIndex, end: endIndex };
            this.mForceUpdate = false;

            let recycledCount = 0;
            this.mVisibleItemsMap.forEach((node, index) => {
                if (index < startIndex || index >= endIndex) {
                    this.RecycleNode(node, index);
                    this.mVisibleItemsMap.delete(index);
                    recycledCount++;
                }
            });

            if (this.mIsDebugMode) {
                this.mRenderStats.recycleCount = recycledCount;
            }

            const newItems = new Set<number>();
            for (let i = startIndex; i < endIndex; i++) {
                if (!this.mVisibleItemsMap.has(i)) {
                    newItems.add(i);
                }
            }

            this.mLoadingQueue = Array.from(newItems);

            if (this.mNeedFrameLoading) {
                if (!this.mLoadingScheduled && this.mLoadingQueue.length > 0) {
                    this.schedule(this.ProcessLoadingQueue, 0);
                    this.mLoadingScheduled = true;
                }
            } else {
                this.LoadAllItemsDirectly();
            }

            if (this.mIsDebugMode) {
                this.mRenderStats.frameTime = Date.now() - startTime;
                this.mRenderStats.visibleCount = this.mVisibleItemsMap.size;
                this.LogDebugInfo();
            }
        }
    }

    /**
     * 处理加载队列
     */
    private ProcessLoadingQueue() {
        const batchSize = this.ComputeLoadParameters().batchSize;
        let processed = 0;

        while (processed < batchSize && this.mLoadingQueue.length > 0) {
            const index = this.mLoadingQueue.shift()!;
            if (this.ProcessSingleItem(index)) {
                processed++;
            }
        }

        if (this.mLoadingQueue.length === 0) {
            this.unschedule(this.ProcessLoadingQueue);
            this.mLoadingScheduled = false;
            this.mNeedFrameLoading = false;
        }
    }

    /**
     * 直接加载所有项目
     */
    private LoadAllItemsDirectly() {
        while (this.mLoadingQueue.length > 0) {
            const index = this.mLoadingQueue.shift()!;
            this.ProcessSingleItem(index);
        }
    }

    /**
     * 处理单个项目
     * @param index 项目索引
     * @returns 是否处理成功
     */
    private ProcessSingleItem(index: number): boolean {
        if (this.mVisibleItemsMap.has(index)) {
            return false;
        }

        const itemData = this.mItemsData[index];
        if (!itemData && this.mTotalItemCount > 0) {
            return false;
        }

        try {
            const node = this.GetTemplateNode(index);
            if (!isValid(node)) {
                return false;
            }

            const isReused = node.__reused__ === true;
            node.active = false;
            node.setParent(this.mContent);

            const transform = node.getComponent(UITransform);
            if (transform) {
                transform.setAnchorPoint(new Vec2(0.5, 0.5));
            }

            this.UpdateNewSize(node, this.mItemSizes[index]);

            let position = Vec3.ZERO;
            const offset = index === 0 ? 0 : this.mCumulativeSizes[index - 1];

            switch (this.layoutType) {
                case ViewLayoutType.VERTICAL:
                    position = new Vec3(0, -(offset + 0.5 * this.mItemSizes[index]), 0);
                    break;
                case ViewLayoutType.HORIZONTAL:
                    position = new Vec3(offset + 0.5 * this.mItemSizes[index], 0, 0);
                    break;
                case ViewLayoutType.GRID:
                    switch (this.scrollDirection) {
                        case ScrollDirection.VERTICAL:
                            const row = Math.floor(index / this.cols);
                            const col = index % this.cols;
                            const centerX = -(this.cols * this.mCellWidth + (this.cols - 1) * this.girdVertColsSpacing) / 2 + this.mCellWidth / 2;
                            position = new Vec3(
                                centerX + col * (this.mCellWidth + this.girdVertColsSpacing),
                                -(row * (this.mCellHeight + this.girdVertRowsSpacing) + this.mCellHeight / 2),
                                0
                            );
                            break;
                        case ScrollDirection.HORIZONTAL:
                            const col2 = Math.floor(index / this.rows);
                            const row2 = index % this.rows;
                            const centerY = (this.rows * this.mCellHeight + (this.rows - 1) * this.girdHorizontalRowsSpacing) / 2 - this.mCellHeight / 2;
                            position = new Vec3(
                                col2 * (this.mCellWidth + this.girdHorizontalColsSpacing) + this.mCellWidth / 2,
                                centerY - row2 * (this.mCellHeight + this.girdHorizontalRowsSpacing),
                                0
                            );
                            break;
                    }
                    break;
            }

            node.setPosition(position);
            node.active = true;

            if (!isReused && this.mItemInitCallback) {
                this.mItemInitCallback(node, index, itemData);
            } else if (this.mItemUpdateCallback) {
                this.mItemUpdateCallback(node, index, itemData);
            }

            node.__reused__ = true;
            this.mVisibleItemsMap.set(index, node);
            return true;
        } catch (error) {
            console.error(`VirtualViewList: Error processing item at index ${index}:`, error);
            return false;
        }
    }

    /**
     * 查找索引
     * @param offset 偏移量
     * @param isStart 是否为起始索引
     * @returns 找到的索引
     */
    private FindIndex(offset: number, isStart: boolean = true): number {
        if (this.mCumulativeSizes.length === 0) {
            return 0;
        }

        if (offset <= 0) {
            return 0;
        }

        if (this.mLastFindCache &&
            offset >= this.mLastFindCache.offset &&
            offset <= this.mLastFindCache.offset + this.mLastFindCache.size) {
            return this.mLastFindCache.index;
        }

        if (offset >= this.mCumulativeSizes[this.mCumulativeSizes.length - 1]) {
            return this.mCumulativeSizes.length;
        }

        let left = 0;
        let right = this.mCumulativeSizes.length - 1;

        while (left <= right) {
            const mid = left + Math.floor((right - left) / 2);
            if (isStart ? this.mCumulativeSizes[mid] < offset : this.mCumulativeSizes[mid] <= offset) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        if (left > 0 && left < this.mCumulativeSizes.length) {
            const prevOffset = left > 0 ? this.mCumulativeSizes[left - 1] : 0;
            const size = this.mCumulativeSizes[left] - prevOffset;
            this.mLastFindCache = { offset: prevOffset, index: left, size };
        }

        return left;
    }

    /**
     * 预加载项目
     * @param count 预加载数量
     */
    public PreloadItems(count: number = this.mAutoPreloadCount) {
        if (this.mTemplateItems.size === 0) {
            console.error('VirtualViewList: 未设置模板节点，无法预加载');
            return;
        }

        let totalPreloaded = 0;
        this.mTemplateItems.forEach((template, type) => {
            const preloadCount = Math.ceil(count / this.mTemplateItems.size);
            for (let i = 0; i < preloadCount; i++) {
                let node: Node | null = null;
                if (template.node instanceof Node) {
                    node = instantiate(template.node);
                } else if (typeof template.node === 'function') {
                    node = template.node();
                }

                if (node) {
                    node.__reused__ = true;
                    template.pool.put(node);
                    totalPreloaded++;
                }
            }
        });

        console.log(`VirtualViewList: 已预加载 ${totalPreloaded} 个节点到对象池`);
    }

    /**
     * 获取总项目数
     * @returns 总项目数
     */
    public GetTotalItemCount(): number {
        return this.mTotalItemCount;
    }

    /**
     * 获取可见项目数
     * @returns 可见项目数
     */
    public GetVisibleItemCount(): number {
        return this.mVisibleItemsMap.size;
    }

    /**
     * 设置更新间隔
     * @param interval 更新间隔（毫秒）
     */
    public SetUpdateInterval(interval: number) {
        this.mUpdateInterval = Math.max(16, interval);
        this.autoOptimizePerformance = false;
    }

    /**
     * 启用调试模式
     * @param enable 是否启用
     */
    public EnableDebugMode(enable: boolean = true) {
        this.mIsDebugMode = enable;
    }

    /**
     * 输出调试信息
     */
    private LogDebugInfo() {
        if (!this.mIsDebugMode) {
            return;
        }

        let poolSize = 0;
        this.mTemplateItems.forEach(item => {
            if (item.pool) {
                poolSize += item.pool.size();
            }
        });

        let layoutTypeStr = '';
        switch (this.layoutType) {
            case ViewLayoutType.VERTICAL:
                layoutTypeStr = '垂直列表';
                break;
            case ViewLayoutType.HORIZONTAL:
                layoutTypeStr = '水平列表';
                break;
            case ViewLayoutType.GRID:
                layoutTypeStr = `网格(${this.scrollDirection === ScrollDirection.VERTICAL ? '垂直滚动' : '水平滚动'})`;
                break;
        }

        console.log(`[VirtualList调试] ${layoutTypeStr}:
        渲染帧耗时: ${this.mRenderStats.frameTime}ms
        可见项数量: ${this.mRenderStats.visibleCount}/${this.mTotalItemCount}
        回收节点数: ${this.mRenderStats.recycleCount}
        对象池大小: ${poolSize}
        更新间隔: ${this.mUpdateInterval}ms 
        滚动速度: ${this.mScrollVelocity.toFixed(2)}
        可见区间: [${this.mLastVisibleIndices.start}, ${this.mLastVisibleIndices.end}]`);
    }

    /**
     * 在指定位置插入项目
     * @param index 插入位置
     * @param item 要插入的项目
     */
    public InsertItemAt(index: number, item: ItemData): void {
        if (index < 0 || index > this.mItemsData.length) {
            console.error('Invalid index for insertion');
            return;
        }

        // Insert the item into the data array
        this.mItemsData.splice(index, 0, item);

        // Update sizes and content
        this.InitializeItemSizes();
        this.UpdateContentSize();

        // Refresh the list
        this.Refresh();
    }

    /**
     * 移除指定位置的项目
     * @param index 要移除的位置
     * @param updateContent 是否更新内容
     */
    public RemoveItemAt(index: number, updateContent: boolean = true): void {
        if (index < 0 || index >= this.mItemsData.length) {
            console.error('Invalid index for removal');
            return;
        }

        // Remove the item from the data array
        this.mItemsData.splice(index, 1);

        if (updateContent) {
            // Update sizes and content
            this.InitializeItemSizes();
            this.UpdateContentSize();

            // Refresh the list
            this.Refresh();
        }
    }
} 