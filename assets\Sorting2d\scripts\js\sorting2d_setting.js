System.register("chunks:///_virtual/sorting2d_setting.ts", ["./rollupPluginModLoBabelHelpers.js", "cc", "./sorts.ts"], (function (t) {
    var i, e, o, n, r, s, p, l, u, c, g;
    return {
        setters: [function (t) {
            i = t.applyDecoratedDescriptor,
                e = t.inherits<PERSON>oose,
                o = t.initializerDefineProperty,
                n = t.assertThisInitialized,
                r = t.createClass
        }
            , function (t) {
                s = t.cclegacy,
                    p = t._decorator,
                    l = t.Vec3,
                    u = t.RenderRoot2D,
                    c = t.Component
            }
            , function (t) {
                g = t.EnumSort2DMode
            }
        ],
        execute: function () {
            var a, d, y, _, h, f, D, m, b;
            s._RF.push({}, "db77f33NQ9GJplpEhjvBrPw", "sorting2d_setting", void 0);
            var x = p.ccclass
                , M = p.property
                , A = p.executeInEditMode
                , w = p.requireComponent
                , S = p.disallowMultiple;
            new l,
                t("Sorting2DSettings", (a = x("Sorting2DSettings"),
                    d = w(u),
                    y = M({
                        type: g
                    }),
                    _ = M({
                        type: g,
                        tooltip: "i18n:Sorting2D.soring_mode"
                    }),
                    h = M({
                        visible: function () {
                            return this.sortingMode == g.CustomAxis
                        },
                        type: l,
                        tooltip: "i18n:Sorting2D.soring_axis"
                    }),
                    a(f = A(f = S(f = d((m = i((D = function (t) {
                        function i() {
                            for (var i, e = arguments.length, r = new Array(e), s = 0; s < e; s++)
                                r[s] = arguments[s];
                            return i = t.call.apply(t, [this].concat(r)) || this,
                                o(i, "_sortingMode", m, n(i)),
                                o(i, "_sortingAxis", b, n(i)),
                                i.planeNormal = new l(0, 0, 1),
                                i._renderRoot = null,
                                i._dirty = !0,
                                i
                        }
                        e(i, t);
                        var s = i.prototype;
                        return s.clearDirty = function () {
                            this._dirty = !1
                        }
                            ,
                            s.onLoad = function () {
                                this._renderRoot = this.getComponent(u),
                                    this.planeNormal.set(this._sortingAxis).normalize()
                            }
                            ,
                            s.onEnable = function () {
                                this._renderRoot.sorting2DSettings = this
                            }
                            ,
                            s.onDisable = function () {
                                this._renderRoot.sorting2DSettings = null
                            }
                            ,
                            r(i, [{
                                key: "sortingMode",
                                get: function () {
                                    return this._sortingMode
                                },
                                set: function (t) {
                                    this._sortingMode = t,
                                        this._dirty = !0
                                }
                            }, {
                                key: "sortingAxis",
                                get: function () {
                                    return this._sortingAxis
                                },
                                set: function (t) {
                                    this._sortingAxis = t,
                                        this.planeNormal.set(this._sortingAxis).normalize(),
                                        this._dirty = !0
                                }
                            }, {
                                key: "dirty",
                                get: function () {
                                    return this._dirty
                                }
                            }]),
                            i
                    }(c)).prototype, "_sortingMode", [y], {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                        initializer: function () {
                            return g.Default
                        }
                    }),
                        i(D.prototype, "sortingMode", [_], Object.getOwnPropertyDescriptor(D.prototype, "sortingMode"), D.prototype),
                        b = i(D.prototype, "_sortingAxis", [M], {
                            configurable: !0,
                            enumerable: !0,
                            writable: !0,
                            initializer: function () {
                                return new l(0, 0, 1)
                            }
                        }),
                        i(D.prototype, "sortingAxis", [h], Object.getOwnPropertyDescriptor(D.prototype, "sortingAxis"), D.prototype),
                        f = D)) || f) || f) || f) || f));
            s._RF.pop()
        }
    }
}
));