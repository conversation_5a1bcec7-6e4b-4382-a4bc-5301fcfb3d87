import { Vec3 } from "cc";
import { Component } from "db://framework/ECS/Component";

/**
 * <AUTHOR>
 * @data 2025-08-02 14:52
 * @filePath assets\lawnMowerDemo_ECS\scripts\components\Transform.ts
 * @description 变换组件 - 存储实体的位置、旋转、缩放信息
 */
export class Transform extends Component {

    private _position: Vec3 = new Vec3();
    public get position(): Vec3 { return this._position; }
    // public set position(value: Vec3) { this._position = value; }
    // 上一帧的位置，用于计算移动方向和距离
    private _previousPosition: Vec3 = new Vec3();
    /** 上一帧的位置 */
    public get previousPosition(): Vec3 { return this._previousPosition; }
    // public set previousPosition(value: Vec3) { this._previousPosition = value; }
    private _rotation: number = 0;
    /** 旋转角度（弧度） */
    public get rotation(): number { return this._rotation; }
    public set rotation(value: number) { this._rotation = value; }

    constructor(x: number = 0, y: number = 0, z: number = 0) {
        super();
        this._position.set(x, y, z);
        this._previousPosition.set(x, y, z);
    }

}