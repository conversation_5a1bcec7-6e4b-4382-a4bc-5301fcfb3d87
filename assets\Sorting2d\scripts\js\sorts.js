System.register("chunks:///_virtual/sorts.ts", ["cc", "./utils.ts"], (function (t) {
    var r, o, n, e;
    return {
        setters: [function (t) {
            r = t.cclegacy,
                o = t.Enum,
                n = t.Camera
        }
            , function (t) {
                e = t.signSqrtDistToPlane
            }
        ],
        execute: function () {
            r._RF.push({}, "746f1ijnYxPZ5gB3xTauNNn", "sorts", void 0);
            var s = t("EnumSort2DMode", o({
                Default: 0,
                Camera: 1,
                CustomAxis: 2
            }))
                , i = t("DefaultSort", function () {
                    function t() { }
                    var r = t.prototype;
                    return r.ready = function (t) { }
                        ,
                        r.compare = function (t, r) {
                            return t.sorting2D.sortingOrder - r.sorting2D.sortingOrder
                        }
                        ,
                        t
                }())
                , a = t("CameraSort", function () {
                    function t() { }
                    var r = t.prototype;
                    return r.ready = function (t, r) {
                        var o = r.camera;
                        o && (t.node.hasChangedFlags || o.node.hasChangedFlags || r.settings.dirty) && (o.projection == n.ProjectionType.ORTHO ? t.sortValue = o.node.worldPosition.z - t.node.worldPosition.z : t.sortValue = e(o.node.forward, o.node.worldPosition, t.node.worldPosition),
                            t.sortValue /= 100)
                    }
                        ,
                        r.compare = function (t, r) {
                            return t.sortValue - r.sortValue
                        }
                        ,
                        t
                }())
                , u = t("CustomAxisSort", function () {
                    function t() { }
                    var r = t.prototype;
                    return r.ready = function (t, r) {
                        if (r.settings && (t.node.hasChangedFlags || r.settings.dirty)) {
                            var o = r.settings;
                            t.sortValue = e(o.planeNormal, r.center, t.node.worldPosition),
                                t.sortValue /= 100
                        }
                    }
                        ,
                        r.compare = function (t, r) {
                            return t.sortValue - r.sortValue
                        }
                        ,
                        t
                }())
                , c = t("SortFactory", function () {
                    function t() { }
                    return t.getSort = function (t) {
                        return this._sortMap.get(t)
                    }
                        ,
                        t.registSort = function (t, r) {
                            this._sorts.push(r),
                                this._sortMap.set(t, r)
                        }
                        ,
                        t.addCustomSort = function (t, r, o) {
                            Object.defineProperty(s, t, {
                                value: r,
                                writable: !1,
                                enumerable: !0,
                                configurable: !0
                            }),
                                this.registSort(r, o)
                        }
                        ,
                        t
                }());
            c._sorts = [],
                c._sortMap = new Map,
                c.registSort(s.Default, new i),
                c.registSort(s.Camera, new a),
                c.registSort(s.CustomAxis, new u),
                r._RF.pop()
        }
    }
}
));