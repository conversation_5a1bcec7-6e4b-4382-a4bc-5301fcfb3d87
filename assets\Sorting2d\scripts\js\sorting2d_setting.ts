import { _decorator, Component, Enum, RenderRoot2D, Vec3 } from 'cc';
import { EnumSort2DMode } from './sorts';

// 扩展 RenderRoot2D 类型定义
declare module 'cc' {
    interface RenderRoot2D {
        sorting2DSettings: Sorting2DSettings | null;
    }
}

const { ccclass, property, executeInEditMode, requireComponent, disallowMultiple } = _decorator;

/**
 * 2D排序设置组件
 */
@ccclass('Sorting2DSettings')
@requireComponent(RenderRoot2D)
@executeInEditMode
@disallowMultiple
export class Sorting2DSettings extends Component {
    @property
    private _sortingMode: EnumSort2DMode = EnumSort2DMode.Default;
    @property({ type: Enum(EnumSort2DMode), tooltip: '2D排序模式枚举' })
    public get sortingMode(): EnumSort2DMode {
        return this._sortingMode;
    }
    public set sortingMode(value: EnumSort2DMode) {
        this._sortingMode = value;
        this._dirty = true;
    }
    @property
    private _sortingAxis: Vec3 = new Vec3(0, 0, 1);
    @property({
        tooltip: '排序轴',
        visible: function (this: Sorting2DSettings) {
            return this.sortingMode === EnumSort2DMode.CustomAxis;
        }
    })
    public get sortingAxis(): Vec3 {
        return this._sortingAxis;
    }
    public set sortingAxis(value: Vec3) {
        this._sortingAxis = value;
        this.planeNormal.set(this._sortingAxis).normalize();
        this._dirty = true;
    }

    public get dirty(): boolean {
        return this._dirty;
    }

    /**
     * 平面法向量
     */
    public planeNormal: Vec3 = new Vec3(0, 0, 1);

    private _renderRoot: RenderRoot2D | null = null;
    private _dirty: boolean = true;

    /**
     * 清除脏标记
     */
    public clearDirty(): void {
        this._dirty = false;
    }

    protected onLoad(): void {
        this._renderRoot = this.getComponent(RenderRoot2D);
        this.planeNormal.set(this._sortingAxis).normalize();
    }

    protected onEnable(): void {
        if (this._renderRoot) {
            this._renderRoot.sorting2DSettings = this;
        }
    }

    protected onDisable(): void {
        if (this._renderRoot) {
            this._renderRoot.sorting2DSettings = null;
        }
    }
} 