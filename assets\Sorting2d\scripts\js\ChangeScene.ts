import { _decorator, Component, director } from 'cc';

const { ccclass } = _decorator;

/**
 * 场景切换组件
 */
@ccclass('ChangeScene')
export class ChangeScene extends Component {
    start(): void {
        // 组件启动时的初始化
    }

    update(deltaTime: number): void {
        // 每帧更新
    }

    /**
     * 切换场景
     * @param event 事件对象
     * @param sceneName 目标场景名称
     */
    changeScene(event: any, sceneName: string): void {
        console.log(sceneName);
        this.scheduleOnce(() => {
            director.loadScene(sceneName);
        }, 0.5);
    }
} 