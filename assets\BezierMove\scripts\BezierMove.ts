import { _decorator, Color, Component, Enum, Graphics, math, Node, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 缓动类型枚举
 */
export enum EasingType {
    linear = 'linear',
    smooth = 'smooth',
    fade = 'fade',
    quadIn = 'quadIn',
    quadOut = 'quadOut',
    quadInOut = 'quadInOut',
    quadOutIn = 'quadOutIn',
    cubicIn = 'cubicIn',
    cubicOut = 'cubicOut',
    cubicInOut = 'cubicInOut',
    cubicOutIn = 'cubicOutIn',
    quartIn = 'quartIn',
    quartOut = 'quartOut',
    quartInOut = 'quartInOut',
    quartOutIn = 'quartOutIn',
    quintIn = 'quintIn',
    quintOut = 'quintOut',
    quintInOut = 'quintInOut',
    quintOutIn = 'quintOutIn',
    sineIn = 'sineIn',
    sineOut = 'sineOut',
    sineInOut = 'sineInOut',
    sineOutIn = 'sineOutIn',
    expoIn = 'expoIn',
    expoOut = 'expoOut',
    expoInOut = 'expoInOut',
    expoOutIn = 'expoOutIn',
    circIn = 'circIn',
    circOut = 'circOut',
    circInOut = 'circInOut',
    circOutIn = 'circOutIn',
    elasticIn = 'elasticIn',
    elasticOut = 'elasticOut',
    elasticInOut = 'elasticInOut',
    elasticOutIn = 'elasticOutIn',
    backIn = 'backIn',
    backOut = 'backOut',
    backInOut = 'backInOut',
    backOutIn = 'backOutIn',
    bounceIn = 'bounceIn',
    bounceOut = 'bounceOut',
    bounceInOut = 'bounceInOut',
    bounceOutIn = 'bounceOutIn'
}

/**
 * 控制点方向枚举
 */
export enum ControlPointSideType {
    Random,
    Left,
    Right,
}

/**
 * 贝塞尔曲线移动组件
 * 用于实现物体沿贝塞尔曲线路径移动的效果
 */
@ccclass('BezierMove')
export class BezierMove extends Component {
    @property({ displayName: '速度', tooltip: '移动速度' })
    public speed: number = 600;

    @property({ type: Enum(ControlPointSideType), displayName: '控制点方向', tooltip: '0=随机, 1=左侧，-1=右侧（相对于从起点到终点的方向）' })
    public controlPointSide: ControlPointSideType = ControlPointSideType.Left;

    @property({ type: Enum(EasingType), displayName: '缓动函数' })
    public easing: EasingType = EasingType.linear;

    @property({ slide: true, range: [0, 1, 0.1], displayName: '控制点偏移系数', tooltip: '0~1之间,控制点偏移系数,值越大曲线越弯曲' })
    public controlPointOffset: number = 0.5;

    @property({ slide: true, range: [0, 1, 0.1], displayName: '控制点随机性', tooltip: '0~1之间,控制点随机性,值越大随机性越强' })
    public controlPointRandomness: number = 0.3;

    @property({ slide: true, range: [0, 1, 0.1], displayName: '角度平滑系数', tooltip: '0~1之间,值越小旋转越平滑,0表示不旋转,1表示立即旋转' })
    public rotationSmoothness: number = 0.1;

    @property({ displayName: '自动旋转' })
    public autoRotate: boolean = true;

    @property({ displayName: '显示轨迹' })
    public showTrajectory: boolean = true;

    @property({ displayName: '轨迹颜色', visible: function (this: BezierMove) { return this.showTrajectory } })
    public trajectoryColor: Color = new Color(0, 255, 0, 255);

    @property({ displayName: '轨迹宽度', visible: function (this: BezierMove) { return this.showTrajectory } })
    public trajectoryWidth: number = 3;

    private _graphics: Graphics | null = null;
    private _currentAngle: number = 0;
    private _startPoint: Vec3 = new Vec3();
    private _endPoint: Vec3 = new Vec3();
    private _controlPoint: Vec3 = new Vec3();
    private _t: number = 0;
    private _totalTime: number = 0;
    private _isMoving: boolean = false;
    private _callback: (() => void) | null = null;
    private _target: any;

    /**
     * 组件启用时调用
     */
    protected onEnable(): void {
        this._startPoint.set(this.node.position);
        this._currentAngle = this.node.angle;
        this._initGraphics();
    }

    /**
     * 组件禁用时调用
     */
    protected onDisable(): void {
        if (this._graphics) {
            this._graphics.clear();
            if (this._graphics.node && this._graphics.node.isValid) {
                this._graphics.node.destroy();
            }
        }
    }

    /**
     * 设置移动完成回调
     * @param callback 回调函数
     * @param target 回调函数的this指向
     */
    public onMoveComplete(callback: () => void, target: any): void {
        this._callback = callback;
        this._target = target;
    }

    /**
     * 每帧更新
     * @param dt 帧间隔时间
     */
    protected update(dt: number): void {
        if (!this._isMoving || this._totalTime <= 0) return;

        this._t += dt / this._totalTime;
        const easedT = this._applyEasing(this._t);

        if (this._t >= 1) {
            this.node.setPosition(this._endPoint);
            this._isMoving = false;
            if (this._callback) {
                this._callback();
            }
            if (this.showTrajectory && this._graphics) {
                this._graphics.clear();
                if (this._graphics.node.parent !== this.node) {
                    const graphicsNode = this._graphics.node;
                    if (graphicsNode.parent) {
                        graphicsNode.removeFromParent();
                    }
                    this.node.addChild(graphicsNode);
                }
            }
        } else {
            const position = this._calculateQuadraticBezierPoint(
                easedT,
                this._startPoint,
                this._controlPoint,
                this._endPoint
            );
            this.node.setPosition(position);

            if (this.autoRotate) {
                const tangent = this._calculateQuadraticBezierTangent(
                    easedT,
                    this._startPoint,
                    this._controlPoint,
                    this._endPoint
                );
                if (tangent.lengthSqr() > 0.001) {
                    const targetAngle = math.toDegree(Math.atan2(tangent.y, tangent.x)) - 90;
                    this._currentAngle = this._smoothAngle(this._currentAngle, targetAngle, this.rotationSmoothness);
                    this.node.angle = this._currentAngle;
                }
            }
        }
    }

    /**
     * 移动到指定位置
     * @param position 目标位置
     */
    public moveTo(position: Vec3): void {
        this._startPoint.set(this.node.position);
        this._endPoint.set(position);
        this._currentAngle = this.node.angle;
        this._generateControlPoint();

        const distance = Vec3.distance(this._startPoint, this._controlPoint) +
            Vec3.distance(this._controlPoint, this._endPoint);
        this._totalTime = distance / this.speed;
        if (this._totalTime < 0.1) {
            this._totalTime = 0.1;
        }

        if (this.showTrajectory) {
            this._drawTrajectory();
        }

        this._t = 0;
        this._isMoving = true;
    }

    /**
     * 停止移动
     */
    public stopMoving(): void {
        this._isMoving = false;
    }

    /**
     * 生成控制点
     * @private
     */
    private _generateControlPoint(): void {
        const midPoint = new Vec3();
        Vec3.lerp(midPoint, this._startPoint, this._endPoint, 0.5);

        const direction = new Vec3();
        Vec3.subtract(direction, this._endPoint, this._startPoint);

        const perpendicular = new Vec3(-direction.y, direction.x, 0);
        Vec3.normalize(perpendicular, perpendicular);

        let side = 1;
        if (this.controlPointSide === ControlPointSideType.Random) {
            side = Math.random() < 0.5 ? 1 : -1;
        } else {
            side = this.controlPointSide === ControlPointSideType.Left ? 1 : -1;
        }

        const offset = Vec3.distance(this._startPoint, this._endPoint) * this.controlPointOffset;
        const randomness = (2 * Math.random() - 1) * this.controlPointRandomness;

        this._controlPoint.set(midPoint.x + perpendicular.x * offset * (1 + randomness) * side, midPoint.y + perpendicular.y * offset * (1 + randomness) * side, 0);
    }

    /**
     * 计算二次贝塞尔曲线上的点
     * @param t 插值系数 (0-1)
     * @param p0 起点
     * @param p1 控制点
     * @param p2 终点
     * @returns 曲线上的点
     * @private
     */
    private _calculateQuadraticBezierPoint(t: number, p0: Vec3, p1: Vec3, p2: Vec3): Vec3 {
        const oneMinusT = 1 - t;
        const t2 = t * t;
        const oneMinusT2 = oneMinusT * oneMinusT;

        const result = new Vec3();
        Vec3.multiplyScalar(result, p0, oneMinusT2);

        const temp = new Vec3();
        Vec3.multiplyScalar(temp, p1, 2 * oneMinusT * t);
        Vec3.add(result, result, temp);

        Vec3.multiplyScalar(temp, p2, t2);
        Vec3.add(result, result, temp);

        return result;
    }

    /**
     * 计算二次贝塞尔曲线的切线
     * @param t 插值系数 (0-1)
     * @param p0 起点
     * @param p1 控制点
     * @param p2 终点
     * @returns 切线向量
     * @private
     */
    private _calculateQuadraticBezierTangent(t: number, p0: Vec3, p1: Vec3, p2: Vec3): Vec3 {
        const oneMinusT = 1 - t;
        const result = new Vec3();
        const temp = new Vec3();

        Vec3.subtract(temp, p1, p0);
        Vec3.multiplyScalar(temp, temp, 2 * oneMinusT);

        const temp2 = new Vec3();
        Vec3.subtract(temp2, p2, p1);
        Vec3.multiplyScalar(temp2, temp2, 2 * t);

        Vec3.add(result, temp, temp2);
        return result;
    }

    /**
     * 应用缓动函数
     * @param t 原始插值系数 (0-1)
     * @returns 应用缓动后的插值系数
     * @private
     */
    private _applyEasing(t: number): number {
        t = Math.max(0, Math.min(1, t));

        switch (this.easing) {
            case EasingType.linear:
                return t;
            case EasingType.smooth:
            case EasingType.fade:
                return t * t * (3 - 2 * t);
            case EasingType.quadIn:
                return t * t;
            case EasingType.quadOut:
                return t * (2 - t);
            case EasingType.quadInOut:
                return t < 0.5 ? 2 * t * t : (4 - 2 * t) * t - 1;
            case EasingType.quadOutIn:
                return t < 0.5 ? 0.5 * (1 - Math.pow(-2 * t + 1, 2)) : 0.5 * Math.pow(2 * t - 1, 2) + 0.5;
            case EasingType.cubicIn:
                return t * t * t;
            case EasingType.cubicOut:
                return --t * t * t + 1;
            case EasingType.cubicInOut:
                return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
            case EasingType.cubicOutIn:
                return t < 0.5 ? 0.5 * ((t = 2 * t - 1) * t * t + 1) : 0.5 * (t = 2 * t - 1) * t * t + 0.5;
            case EasingType.quartIn:
                return t * t * t * t;
            case EasingType.quartOut:
                return 1 - --t * t * t * t;
            case EasingType.quartInOut:
                return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;
            case EasingType.quartOutIn:
                return t < 0.5 ? 0.5 * (1 - Math.pow(-2 * t + 1, 4)) : 0.5 * Math.pow(2 * t - 1, 4) + 0.5;
            case EasingType.quintIn:
                return t * t * t * t * t;
            case EasingType.quintOut:
                return 1 + --t * t * t * t * t;
            case EasingType.quintInOut:
                return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;
            case EasingType.quintOutIn:
                return t < 0.5 ? 0.5 * (1 - Math.pow(-2 * t + 1, 5)) : 0.5 * Math.pow(2 * t - 1, 5) + 0.5;
            case EasingType.sineIn:
                return 1 - Math.cos(t * Math.PI / 2);
            case EasingType.sineOut:
                return Math.sin(t * Math.PI / 2);
            case EasingType.sineInOut:
                return 0.5 * (1 - Math.cos(Math.PI * t));
            case EasingType.sineOutIn:
                return t < 0.5 ? 0.5 * Math.sin(t * Math.PI) : 0.5 - 0.5 * Math.cos((2 * t - 1) * Math.PI / 2);
            case EasingType.circIn:
                return 1 - Math.sqrt(1 - t * t);
            case EasingType.circOut:
                return Math.sqrt(1 - (t - 1) * (t - 1));
            case EasingType.circInOut:
                return t < 0.5 ? (1 - Math.sqrt(1 - 4 * t * t)) / 2 : (Math.sqrt(1 - Math.pow(-2 * t + 2, 2)) + 1) / 2;
            case EasingType.circOutIn:
                return t < 0.5 ? 0.5 * Math.sqrt(1 - Math.pow(-2 * t + 1, 2)) : 0.5 * (2 - Math.sqrt(1 - Math.pow(2 * t - 1, 2)));
            case EasingType.expoIn:
                return t === 0 ? 0 : Math.pow(2, 10 * t - 10);
            case EasingType.expoOut:
                return t === 1 ? 1 : 1 - Math.pow(2, -10 * t);
            case EasingType.expoInOut:
                return t === 0 ? 0 : t === 1 ? 1 : t < 0.5 ? Math.pow(2, 20 * t - 10) / 2 : (2 - Math.pow(2, -20 * t + 10)) / 2;
            case EasingType.expoOutIn:
                return t === 0 ? 0 : t === 1 ? 1 : t < 0.5 ? 0.5 * (1 - Math.pow(2, -20 * t)) : 0.5 * Math.pow(2, 20 * (t - 0.5) - 10) + 0.5;
            case EasingType.backIn:
                const c1 = 1.70158;
                return t * t * ((c1 + 1) * t - c1);
            case EasingType.backOut:
                const c2 = 1.70158;
                return (t -= 1) * t * ((c2 + 1) * t + c2) + 1;
            case EasingType.backInOut:
                const c3 = 2.5949095;
                return t < 0.5 ? 2 * t * (2 * t) * ((c3 + 1) * (2 * t) - c3) / 2 : ((2 * t - 2) * (2 * t - 2) * ((c3 + 1) * (2 * t - 2) + c3) + 2) / 2;
            case EasingType.backOutIn:
                const c4 = 1.70158;
                return t < 0.5 ? 0.5 * ((t = 2 * t - 1) * t * ((c4 + 1) * t + c4) + 1) : 0.5 * (t = 2 * t - 1) * t * ((c4 + 1) * t - c4) + 0.5;
            case EasingType.elasticIn:
                return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * (t - 1)) * Math.sin(5 * (t - 1.1) * Math.PI);
            case EasingType.elasticOut:
                return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin(5 * (t - 0.1) * Math.PI) + 1;
            case EasingType.elasticInOut:
                return t === 0 ? 0 : t === 1 ? 1 : t < 0.5 ? -0.5 * Math.pow(2, 20 * t - 10) * Math.sin((20 * t - 11.125) * Math.PI / 2.25) : 0.5 * Math.pow(2, -20 * t + 10) * Math.sin((20 * t - 11.125) * Math.PI / 2.25) + 1;
            case EasingType.elasticOutIn:
                return t === 0 ? 0 : t === 1 ? 1 : t < 0.5 ? 0.5 * Math.pow(2, -20 * t) * Math.sin(5 * (20 * t - 0.5) * Math.PI) + 0.5 : 0.5 * -Math.pow(2, 10 * (2 * t - 1.5)) * Math.sin(5 * (2 * t - 1.6) * Math.PI) + 0.5;
            case EasingType.bounceIn:
                return 1 - this._bounceOut(1 - t);
            case EasingType.bounceOut:
                return this._bounceOut(t);
            case EasingType.bounceInOut:
                return t < 0.5 ? (1 - this._bounceOut(1 - 2 * t)) / 2 : (1 + this._bounceOut(2 * t - 1)) / 2;
            case EasingType.bounceOutIn:
                return t < 0.5 ? this._bounceOut(2 * t) / 2 : (1 - this._bounceOut(2 - 2 * t)) / 2 + 0.5;
            default:
                return t;
        }
    }

    /**
     * 计算弹跳缓动
     * @param t 插值系数 (0-1)
     * @returns 弹跳后的值
     * @private
     */
    private _bounceOut(t: number): number {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    }

    /**
     * 初始化图形组件
     * @private
     */
    private _initGraphics(): void {
        const graphicsName = `TrajectoryGraphics_${this.node.uuid}`;
        let graphicsNode = this.node.parent?.getChildByName(graphicsName);

        if (!graphicsNode) {
            graphicsNode = new Node(graphicsName);
            if (this.node.parent) {
                this.node.parent.addChild(graphicsNode);
            } else {
                this.node.addChild(graphicsNode);
            }
        }

        this._graphics = graphicsNode.getComponent(Graphics);
        if (!this._graphics) {
            this._graphics = graphicsNode.addComponent(Graphics);
        }
    }

    /**
     * 绘制轨迹
     * @private
     */
    private _drawTrajectory(): void {
        if (!this.showTrajectory || !this._graphics) return;

        this._graphics.clear();
        this._graphics.lineWidth = this.trajectoryWidth;
        this._graphics.strokeColor = this.trajectoryColor;

        const graphicsNode = this._graphics.node;
        if (this.node.parent) {
            graphicsNode.parent = this.node.parent;
            this._graphics.moveTo(this._startPoint.x, this._startPoint.y);
            this._graphics.quadraticCurveTo(this._controlPoint.x, this._controlPoint.y, this._endPoint.x, this._endPoint.y);
        }
        this._graphics.stroke();
    }

    /**
     * 平滑角度插值
     * @param current 当前角度
     * @param target 目标角度
     * @param smoothness 平滑系数
     * @returns 插值后的角度
     * @private
     */
    private _smoothAngle(current: number, target: number, smoothness: number): number {
        smoothness = Math.max(0.001, Math.min(1, smoothness));

        const normalizeAngle = (angle: number): number => {
            angle %= 360;
            return angle < 0 ? angle + 360 : angle;
        };

        const currentNormalized = normalizeAngle(current);
        let delta = normalizeAngle(target) - currentNormalized;

        if (delta > 180) {
            delta -= 360;
        } else if (delta < -180) {
            delta += 360;
        }

        return current + delta * smoothness;
    }

    /**
     * 清除轨迹
     */
    public clearTrajectory(): void {
        if (this._graphics) {
            this._graphics.clear();
        }
    }
} 