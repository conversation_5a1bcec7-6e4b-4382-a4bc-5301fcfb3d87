import { find } from "cc";
import { DEBUG } from "cc/env";
import { AudioManager } from "../manager/audio/AudioManager";
import { EventManager } from "../manager/event/EventManager";
import { GameManager } from "../manager/game/GameManager";
import { NetManager } from "../manager/network/NetManager";
import { StorageManager } from "../manager/storage/StorageManager";
import { TimerManager } from "../manager/timer/TimerManager";
import { UIManager } from "../manager/ui/UIManager";
import { ResLoader } from "./loader/ResLoader";
/**
 * 框架核心应用类
 * @description 负责管理所有全局单例管理器，提供统一的访问入口
 * 包括UI、计时器、事件、音频、存储、资源、游戏、网络等核心功能模块
 * 所有管理器都是单例模式，通过app类统一访问
 */
export class APP {

    /** 
     * UI管理器
     * @description 负责所有界面相关的管理，包括界面打开、关闭、层级控制等
     * 提供界面生命周期管理、界面缓存、界面动画等功能
     */
    public static ui: UIManager = null!;

    /** 
     * 计时器管理器
     * @description 负责游戏内所有定时器、延时器、循环器的管理
     * 提供定时任务、延时执行、循环执行等功能
     */
    public static timer: TimerManager = null!;

    /** 
     * 事件管理器
     * @description 负责全局事件的注册、分发和监听管理
     * 提供事件订阅、发布、取消订阅等功能
     */
    public static event: EventManager = null!;

    /** 
     * 音频管理器
     * @description 负责游戏内所有音效、背景音乐的管理和播放控制
     * 提供音频播放、暂停、恢复、音量控制等功能
     */
    public static audio: AudioManager = null!;

    /** 
     * 存档管理器
     * @description 负责游戏数据的本地存储、读取和加密管理
     * 提供数据持久化、加密存储、缓存管理等功能
     */
    public static storage: StorageManager = null!;

    /** 
     * 资源加载器
     * @description 负责游戏资源的加载、缓存和释放管理
     * 提供资源预加载、异步加载、资源释放等功能
     */
    public static res: ResLoader = null!;

    /** 
     * 游戏管理器
     * @description 负责游戏核心逻辑、状态和流程的管理
     * 提供游戏初始化、状态管理、场景控制等功能
     */
    public static game: GameManager = null!;

    /** 
     * 网络管理器
     * @description 负责网络连接、数据收发和协议处理的管理
     * 提供网络请求、数据序列化、错误处理等功能
     */
    public static net: NetManager = null!;

    /**
     * 设置需要常驻节点的管理器
     * @description 将各个管理器节点添加到常驻节点下，确保场景切换时不会销毁
     * 这些管理器需要在游戏运行期间一直存在，用于管理全局功能
     */
    private static setManagerForPersist(): void {
        const persist = find("FrameworkPersistNode")!;
        persist.addChild(APP.ui.node);
        persist.addChild(APP.timer.node);
        persist.addChild(APP.event.node);
        persist.addChild(APP.audio.node);
        persist.addChild(APP.storage.node);
    }

}
// 在调试模式下，将app实例挂载到window对象上，方便在控制台调试
if (DEBUG) {
    //@ts-ignore
    window.APP = APP;
}