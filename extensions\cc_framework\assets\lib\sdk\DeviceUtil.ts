import { __private, Rect, sys } from "cc";

/**
 * 设备工具类
 * @description 提供设备相关的工具方法
 * 包括平台检测、系统信息获取、安全区域适配等功能
 * 支持主流移动平台和小游戏平台的检测
 */
export class DeviceUtil {
    /** 
     * 获取设备安全区域
     * @description 返回手机屏幕安全区域，如果不是异形屏将默认返回设计分辨率尺寸
     * 目前支持安卓、iOS原生平台和微信小游戏平台
     * @returns 安全区域矩形
     */
    public static getSafeAreaRect(): Rect { return sys.getSafeAreaRect(); }

    /** 
     * 当前运行平台
     * @description 返回当前游戏运行的平台类型
     * @returns 平台类型，如ANDROID、IOS、WECHAT_GAME等
     */
    public static get platform(): __private._pal_system_info_enum_type_platform__Platform { return sys.platform; }

    /** 
     * 当前操作系统
     * @description 返回当前设备的操作系统类型
     * @returns 操作系统类型，如Android、iOS、Windows等
     */
    public static get os(): __private._pal_system_info_enum_type_operating_system__OS { return sys.os; }

    /** 
     * 是否为原生环境
     * @description 判断当前是否在原生平台（Android/iOS）运行
     * @returns 如果是原生环境返回true，否则返回false
     */
    public static get isNative(): boolean { return sys.isNative; }

    /** 
     * 是否为浏览器环境
     * @description 判断当前是否在浏览器中运行
     * @returns 如果是浏览器环境返回true，否则返回false
     */
    public static get isBrowser(): boolean { return sys.isBrowser; }

    /** 
     * 是否为移动设备
     * @description 判断当前是否为移动设备（手机或平板）
     * @returns 如果是移动设备返回true，否则返回false
     */
    public static get isMobile(): boolean { return sys.isMobile; }

    /** 
     * 是否为安卓设备
     * @description 判断当前是否为安卓设备
     * @returns 如果是安卓设备返回true，否则返回false
     */
    public static get isAndroid(): boolean { return sys.platform === sys.Platform.ANDROID; }

    /** 
     * 是否为苹果设备
     * @description 判断当前是否为苹果设备（iPhone/iPad）
     * @returns 如果是苹果设备返回true，否则返回false
     */
    public static get isIPhone(): boolean { return sys.platform === sys.Platform.IOS; }

    /** 
     * 是否为手机浏览器
     * @description 判断当前是否在手机浏览器中运行
     * @returns 如果是手机浏览器返回true，否则返回false
     */
    public static get isMobileBrowser(): boolean { return sys.platform === sys.Platform.MOBILE_BROWSER; }

    /** 
     * 是否为桌面浏览器
     * @description 判断当前是否在桌面浏览器中运行
     * @returns 如果是桌面浏览器返回true，否则返回false
     */
    public static get isDesktopBrowser(): boolean { return sys.platform === sys.Platform.DESKTOP_BROWSER; }

    /** 
     * 是否为微信小游戏
     * @description 判断当前是否在微信小游戏平台运行
     * @returns 如果是微信小游戏返回true，否则返回false
     */
    public static get isWeChat(): boolean { return sys.platform === sys.Platform.WECHAT_GAME; }

    /** 
     * 是否为字节小游戏
     * @description 判断当前是否在字节小游戏平台运行
     * @returns 如果是字节小游戏返回true，否则返回false
     */
    public static get isByteDance(): boolean { return sys.platform === sys.Platform.BYTEDANCE_MINI_GAME; }

    /** 
     * 是否为vivo小游戏
     * @description 判断当前是否在vivo小游戏平台运行
     * @returns 如果是vivo小游戏返回true，否则返回false
     */
    public static get isVivo(): boolean { return sys.platform === sys.Platform.VIVO_MINI_GAME; }

    /** 
     * 是否为OPPO小游戏
     * @description 判断当前是否在OPPO小游戏平台运行
     * @returns 如果是OPPO小游戏返回true，否则返回false
     */
    public static get isOPPO(): boolean { return sys.platform === sys.Platform.OPPO_MINI_GAME; }

    /** 
     * 是否为小米小游戏
     * @description 判断当前是否在小米小游戏平台运行
     * @returns 如果是小米小游戏返回true，否则返回false
     */
    public static get isXiaomi(): boolean { return sys.platform === sys.Platform.XIAOMI_QUICK_GAME; }

    /** 
     * 是否为华为小游戏
     * @description 判断当前是否在华为小游戏平台运行
     * @returns 如果是华为小游戏返回true，否则返回false
     */
    public static get isHuawei(): boolean { return sys.platform === sys.Platform.HUAWEI_QUICK_GAME; }

    /** 
     * 是否为支付宝小游戏
     * @description 判断当前是否在支付宝小游戏平台运行
     * @returns 如果是支付宝小游戏返回true，否则返回false
     */
    public static get isAlipay(): boolean { return sys.platform === sys.Platform.ALIPAY_MINI_GAME; }

    /** 
     * 是否为开源鸿蒙小游戏
     * @description 判断当前是否在开源鸿蒙小游戏平台运行
     * @returns 如果是开源鸿蒙小游戏返回true，否则返回false
     */
    public static get isOpenHarmony(): boolean { return sys.platform === sys.Platform.OPENHARMONY; }
}