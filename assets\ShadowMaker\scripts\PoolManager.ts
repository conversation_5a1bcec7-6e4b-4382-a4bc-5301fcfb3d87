import { _decorator, Component, instantiate, Node, NodePool, Prefab } from 'cc';
const { ccclass } = _decorator;

/**
 * 对象池管理器
 * 用于管理游戏对象的复用
 */
@ccclass('PoolManager')
export class PoolManager extends Component {
    private _nodePools: { [key: string]: NodePool } = {};

    /**
     * 将节点放入对象池
     * @param node 要放入对象池的节点
     */
    public put(node: Node): void {
        if (!node) return;

        const name = node.name;
        const pool = this._nodePools[name];

        if (pool) {
            pool.put(node);
        } else {
            const newPool = new NodePool();
            newPool.put(node);
            this._nodePools[name] = newPool;
        }
    }

    /**
     * 从对象池获取节点
     * @param prefab 预制体
     * @returns 从对象池获取的节点，如果对象池为空则创建新节点
     */
    public get(prefab: Prefab): Node {
        if (!prefab) {
            throw new Error('Prefab cannot be null');
        }

        const name = prefab.name;
        const pool = this._nodePools[name];

        if (pool && pool.size() > 0) {
            const node = pool.get();
            if (!node) {
                return instantiate(prefab);
            }
            return node;
        } else {
            const newPool = new NodePool();
            this._nodePools[name] = newPool;
            return instantiate(prefab);
        }
    }

    /**
     * 获取单例实例
     */
    private static _instance: PoolManager | null = null;
    public static get instance(): PoolManager {
        if (!this._instance) {
            this._instance = new PoolManager();
        }
        return this._instance;
    }
} 