import { <PERSON>, director, <PERSON><PERSON>, Node, PhysicsSystem2D } from "cc";
import { Scene } from "db://framework/ECS/Scene";
import { MovementSystem } from "./systems/MovementSystem";
import { PlayerInputSystem } from "./systems/PlayerInputSystem";
import { AISystem } from "./systems/AISystem";

/**
 * <AUTHOR>
 * @data 2025-08-02 12:03
 * @filePath assets\lawnMowerDemo_ECS\scripts\GameScene.ts
 * @description 游戏场景
 */
export class GameScene extends Scene {

    private gameContainer: Node = null!;
    private mainCamera: Camera = null!;

    protected onLoad(): void {
        this.name = "lawnMowerScene";
        this.enablePhysics();
        this.createGameContainer();

        this.addSystem(new PlayerInputSystem());
        this.addSystem(new MovementSystem());
        this.addSystem(new AISystem());
        this.addSystem(new WeaponSystem());
        // this.addSystem(new ProjectileSystem());
        // this.addSystem(new AirStrikeSystem());
        // this.addSystem(new PowerUpSpawner());
        // this.addSystem(new CollisionSystem());
        // this.addSystem(new CollectibleSystem());
        // this.addSystem(new HealthSystem());
        // this.addSystem(new ParticleSystem());
        // this.addSystem(new CameraFollowSystem());
        // this.addSystem(new PhysicsSystem());

        // this.renderSystem = new RenderSystem();
        // this.addSystem(this.renderSystem);

        // this.enemySpawnSystem = new EnemySpawnSystem();
        // this.addSystem(this.enemySpawnSystem);

        // this.cameraShakeSystem = new CameraShakeSystem();
        // this.addSystem(this.cameraShakeSystem);

        this.setupSystemDependencies();

        // this.createPlayer();
        // this.createEnemySpawner();
    }
    /** 设置系统依赖关系 */
    private setupSystemDependencies(): void {
        // if (this.gameContainer) {
        //     this.renderSystem?.setGameContainer(this.gameContainer);

        //     const weaponSystem = this.getEntityProcessor(WeaponSystem);
        //     if (weaponSystem) {
        //         weaponSystem.setGameContainer(this.gameContainer);
        //     }
        // }

        // if (this.mainCamera) {
        //     const cameraFollowSystem = this.getEntityProcessor(CameraFollowSystem);
        //     if (cameraFollowSystem) {
        //         cameraFollowSystem.setCamera(this.mainCamera);
        //     }
        // }
    }
    /** 启用物理系统 */
    private enablePhysics(): void {
        PhysicsSystem2D.instance.enable = true;
    }
    /** 创建游戏容器 */
    private createGameContainer(): void {
        const canvas = director.getScene()?.getChildByName("Canvas");
        if (!canvas) return;
        this.gameContainer = new Node("GameContainer");
        this.gameContainer.parent = canvas;
        this.gameContainer.layer = Layers.Enum.UI_2D;
        this.mainCamera = canvas.getChildByName("Camera")?.getComponent(Camera)!;
    }

}