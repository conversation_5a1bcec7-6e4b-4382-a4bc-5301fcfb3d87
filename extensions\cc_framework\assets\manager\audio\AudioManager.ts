import { _decorator, AudioClip } from 'cc';
import { BaseManager } from '../../base/BaseManager';
import { APP } from '../../core/APP';
import { AudioEffectPool } from './AudioEffectPool';
import { AudioMusic } from './AudioMusic';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 16:47
 * @filePath assets\core\manager\audio\AudioManager.ts
 * @description 音频管理器 - 负责管理游戏中的背景音乐和音效
 * 提供音频的播放、暂停、恢复、音量控制等功能
 */
@ccclass('AudioManager')
export class AudioManager extends BaseManager {
    /** 本地存储键名 - 用于保存音频设置 */
    private readonly LOCAL_STORE_KEY: string = "game_audio";

    /** 背景音乐管理器 - 负责背景音乐的播放控制 */
    private music: AudioMusic = null!;
    /** 音效管理器 - 负责音效的播放和对象池管理 */
    private effect: AudioEffectPool = new AudioEffectPool();

    /** 
     * 音乐管理状态数据
     * volume_music: 背景音乐音量
     * volume_effect: 音效音量
     * switch_music: 背景音乐开关
     * switch_effect: 音效开关
     */
    private local_data: { volume_music: number, volume_effect: number, switch_music: boolean, switch_effect: boolean } = { volume_music: 1, volume_effect: 1, switch_music: true, switch_effect: true };

    /**
     * 获取背景音乐开关值
     */
    public get switchMusic(): boolean {
        return this.music.switch;
    }

    /**
     * 设置背景音乐开关值
     * @param value     开关值
     */
    public set switchMusic(value: boolean) {
        this.music.switch = value;
        if (!value) this.music.stop();
        this.save();
    }

    /** 保存音乐音效的音量、开关配置数据到本地 */
    private save(): void {
        this.local_data.volume_music = this.music.volume;
        this.local_data.volume_effect = this.effect.volume;
        this.local_data.switch_music = this.music.switch;
        this.local_data.switch_effect = this.effect.switch;

        APP.storage.setItem(this.LOCAL_STORE_KEY, this.local_data);
    }
    /**
     * 组件加载时调用
     * 初始化音频管理器，加载本地配置
     */
    protected onLoad(): void {
        // 本地加载音乐音效的音量、开关配置数据并设置到游戏中
        this.music = this.getComponent(AudioMusic) || this.addComponent(AudioMusic)!;

        this.local_data = APP.storage.getItem(this.LOCAL_STORE_KEY, this.local_data);
        this.music.volume = this.local_data.volume_music;
        this.effect.volume = this.local_data.volume_effect;
        this.music.switch = this.local_data.switch_music;
        this.effect.switch = this.local_data.switch_effect;
        this.log("音频管理器初始化完成");
    }

    /**
     * 恢复当前暂停的音乐与音效播放
     * 用于游戏从后台恢复时的音频恢复
     */
    public resumeAll(): void {
        if (!this.music.playing && this.music.progress > 0) this.music.play();
        this.effect.play();
    }

    /**
     * 暂停当前音乐与音效的播放
     * 用于游戏进入后台时的音频暂停
     */
    public pauseAll(): void {
        if (this.music.playing) this.music.pause();
        this.effect.pause();
    }

    //#region 音效
    /**
     * 播放音效
     * @param url        资源地址或音频剪辑对象
     * @param bundleName 资源包名（可选）
     * @param onPlayComplete 播放完成回调（可选）
     * @returns Promise<number> 返回音效ID
     * @example
     * // 播放一个音效
     * app.audio.playEffect("audio/click");
     * 
     * // 播放音效并监听完成
     * app.audio.playEffect("audio/explosion", null, () => {
     *     console.log("音效播放完成");
     * });
     */
    public playEffect(url: string | AudioClip, bundleName?: string, onPlayComplete?: Function): Promise<number> {
        return this.effect.load(url, bundleName, onPlayComplete);
    }
    //#endregion

    //#region 背景音乐
    /**
     * 设置背景音乐播放完成回调
     * @param callback 背景音乐播放完成回调
     */
    public setMusicComplete(callback: Function | null = null): void {
        this.music.onComplete = callback;
    }

    /**
     * 播放背景音乐
     * @param url        资源地址
     * @param callback   音乐播放完成事件
     * @param bundleName 资源包名
     */
    public playMusic(url: string, callback?: Function, bundleName?: string): void {
        if (this.music.switch) {
            this.music.loop = false;
            this.music.load(url, callback, bundleName).then();
        }
    }

    /** 循环播放背景音乐 */
    public playMusicLoop(url: string, bundleName?: string): void {
        if (this.music.switch) {
            this.music.loop = true;
            this.music.load(url, null!, bundleName).then();
        }
    }

    /** 停止背景音乐播放 */
    public stopMusic(): void {
        if (this.music.switch && this.music.playing) {
            this.music.stop();
        }
    }
    //#endregion
}