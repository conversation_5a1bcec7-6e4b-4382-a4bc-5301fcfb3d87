/**
 * 字符串工具类
 * @description 提供字符串相关的常用工具方法
 * 包括唯一标识生成、空值判断、参数替换、字符串截取等功能
 */
export class StringUtil {
    /** 
     * 生成全局唯一标识符
     * @description 生成一个32位的唯一标识字符串，格式为：xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
     * @returns 生成的唯一标识字符串
     */
    public static guid(): string {
        let guid: string = "";
        for (let i = 1; i <= 32; i++) {
            let n = Math.floor(Math.random() * 16.0).toString(16);
            guid += n;
            if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                guid += "-";
        }
        return guid;
    }
    /** 
     * 判断字符串是否为空
     * @description 检查字符串是否为null、undefined或空字符串
     * @param str 要检查的字符串
     * @returns 如果字符串为空返回true，否则返回false
     */
    public static isEmpty(str: string | null | undefined): boolean {
        return str == null || str == undefined || str == "";
    }
    /**
     * 字符串参数替换
     * @description 将字符串中的{0}、{1}等占位符替换为对应的参数值
     * 支持数组参数和可变参数两种方式
     * @param str 包含占位符的原始字符串
     * @param rest 要替换的参数，可以是数组或可变参数
     * @returns 替换后的字符串
     * @example
     * StringUtil.Substitute("Hello {0}!", "World") // 返回 "Hello World!"
     * StringUtil.Substitute("Name: {0}, Age: {1}", ["John", 25]) // 返回 "Name: John, Age: 25"
     */
    public static Substitute(str: string, ...rest: any[]): string {
        if (str == null) return '';

        let len: number = rest.length;
        let args: any[];
        if (len == 1 && rest[0] instanceof Array) {
            args = rest[0];
            len = args.length;
        } else {
            args = rest;
        }
        for (let i: number = 0; i < len; i++) {
            str = str.replace(new RegExp("\\{" + i + "\\}", "g"), this.isEmpty(args[i]) ? "" : args[i]);
        }
        return str;
    }
    /**
     * 字符串截取
     * @description 根据指定长度截取字符串，支持中英文混合
     * 中文字符按2个长度计算，英文字符按1个长度计算
     * @param str 要截取的字符串
     * @param n 截取长度
     * @param showDot 是否在截取后添加省略号
     * @returns 截取后的字符串
     * @example
     * StringUtil.sub("Hello World", 5) // 返回 "Hello"
     * StringUtil.sub("你好世界", 3, true) // 返回 "你好..."
     */
    public static sub(str: string, n: number, showDot: boolean = false): string {
        if (StringUtil.isEmpty(str)) return "";
        const r = /[^\x00-\xff]/g;
        if (str.replace(r, "mm").length <= n) { return str; }
        const m = Math.floor(n / 2);
        for (let i = m; i < str.length; i++) {
            if (str.substring(0, i).replace(r, "mm").length >= n) {
                if (showDot) {
                    return str.substring(0, i) + "...";
                } else {
                    return str.substring(0, i);
                }
            }
        }
        return str;
    }
}