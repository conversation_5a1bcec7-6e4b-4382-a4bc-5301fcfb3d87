System.register("chunks:///_virtual/sorting-2d.ts", ["./rollupPluginModLoBabelHelpers.js", "cc"], (function (r) {
    var t, e, o, n, i, s, p, u, g, a, l;
    return {
        setters: [function (r) {
            t = r.applyDecoratedDescriptor,
                e = r.inherits<PERSON>oose,
                o = r.initializerDefineProperty,
                n = r.assertThisInitialized,
                i = r.createClass
        }
            , function (r) {
                s = r.cclegacy,
                    p = r._decorator,
                    u = r.UIRenderer,
                    g = r.SortingLayers,
                    a = r.CCInteger,
                    l = r.Component
            }
        ],
        execute: function () {
            var c, y, _, f, h, d, b, D, O, S, G, L, I, m, w, A, v;
            s._RF.push({}, "8ede4BOxH5C15f7jLpP0k9J", "sorting-2d", void 0);
            var z = p.ccclass
                , k = p.requireComponent
                , P = p.property
                , j = p.executeInEditMode
                , C = p.disallowMultiple;
            r("DEFAULT_SORTING2D", {
                sortingLayer: 0,
                sortingOrder: 0,
                sortingAsGroup: !1,
                sortInGroup: !0,
                ignoreSort: !1,
                clone: function () {
                    return {
                        sortingLayer: this.sortingLayer,
                        sortingOrder: this.sortingOrder,
                        sortingAsGroup: this.sortingAsGroup,
                        sortInGroup: this.sortInGroup,
                        ignoreSort: this.ignoreSort
                    }
                }
            }),
                r("Sorting2D", (c = z("Sorting2D"),
                    y = k(u),
                    _ = P({
                        visible: !1,
                        serializable: !0
                    }),
                    f = P({
                        type: g.Enum,
                        tooltip: "i18n:Sorting2D.sorting_layer_name"
                    }),
                    h = P({
                        visible: !1,
                        serializable: !0
                    }),
                    d = P({
                        type: a,
                        tooltip: "i18n:Sorting2D.sorting_order"
                    }),
                    b = P({
                        visible: !1,
                        serializable: !0
                    }),
                    D = P({
                        tooltip: "i18n:Sorting2D.sorting_as_group"
                    }),
                    O = P({
                        tooltip: "i18n:Sorting2D.sort_in_group"
                    }),
                    S = P({
                        tooltip: "i18n:Sorting2D.ignore_sort"
                    }),
                    c(G = y(G = C(G = j((I = t((L = function (r) {
                        function t() {
                            for (var t, e = arguments.length, i = new Array(e), s = 0; s < e; s++)
                                i[s] = arguments[s];
                            return (t = r.call.apply(r, [this].concat(i)) || this)._renderer = null,
                                o(t, "_sortingLayer", I, n(t)),
                                o(t, "_sortingOrder", m, n(t)),
                                o(t, "_sortingAsGroup", w, n(t)),
                                o(t, "_sortInGroup", A, n(t)),
                                o(t, "_ignoreSort", v, n(t)),
                                t
                        }
                        e(t, r);
                        var s = t.prototype;
                        return s.onLoad = function () {
                            this._renderer = this.getComponent(u)
                        }
                            ,
                            s.onEnable = function () {
                                this._renderer && (this._renderer.sorting2D = this,
                                    this._renderer.useDefaultSort = !1)
                            }
                            ,
                            s.onDisable = function () {
                                this._renderer && (this._renderer.useDefaultSort = !0,
                                    this._renderer.sorting2D = null)
                            }
                            ,
                            i(t, [{
                                key: "sortingLayer",
                                get: function () {
                                    return this._sortingLayer
                                }
                            }, {
                                key: "sortingLayerName",
                                get: function () {
                                    return this._sortingLayer
                                },
                                set: function (r) {
                                    this._sortingLayer != r && (this._sortingLayer = r)
                                }
                            }, {
                                key: "sortingOrder",
                                get: function () {
                                    return this._sortingOrder
                                },
                                set: function (r) {
                                    this._sortingOrder = r
                                }
                            }, {
                                key: "sortingAsGroup",
                                get: function () {
                                    return this._sortingAsGroup
                                },
                                set: function (r) {
                                    this._sortingAsGroup = r
                                }
                            }, {
                                key: "sortInGroup",
                                get: function () {
                                    return this._sortInGroup
                                },
                                set: function (r) {
                                    this._sortInGroup = r
                                }
                            }, {
                                key: "ignoreSort",
                                get: function () {
                                    return this._ignoreSort
                                },
                                set: function (r) {
                                    this._ignoreSort = r
                                }
                            }]),
                            t
                    }(l)).prototype, "_sortingLayer", [_], {
                        configurable: !0,
                        enumerable: !0,
                        writable: !0,
                        initializer: function () {
                            return 0
                        }
                    }),
                        t(L.prototype, "sortingLayerName", [f], Object.getOwnPropertyDescriptor(L.prototype, "sortingLayerName"), L.prototype),
                        m = t(L.prototype, "_sortingOrder", [h], {
                            configurable: !0,
                            enumerable: !0,
                            writable: !0,
                            initializer: function () {
                                return 0
                            }
                        }),
                        t(L.prototype, "sortingOrder", [d], Object.getOwnPropertyDescriptor(L.prototype, "sortingOrder"), L.prototype),
                        w = t(L.prototype, "_sortingAsGroup", [b], {
                            configurable: !0,
                            enumerable: !0,
                            writable: !0,
                            initializer: function () {
                                return !1
                            }
                        }),
                        t(L.prototype, "sortingAsGroup", [D], Object.getOwnPropertyDescriptor(L.prototype, "sortingAsGroup"), L.prototype),
                        A = t(L.prototype, "_sortInGroup", [P], {
                            configurable: !0,
                            enumerable: !0,
                            writable: !0,
                            initializer: function () {
                                return !1
                            }
                        }),
                        t(L.prototype, "sortInGroup", [O], Object.getOwnPropertyDescriptor(L.prototype, "sortInGroup"), L.prototype),
                        v = t(L.prototype, "_ignoreSort", [P], {
                            configurable: !0,
                            enumerable: !0,
                            writable: !0,
                            initializer: function () {
                                return !1
                            }
                        }),
                        t(L.prototype, "ignoreSort", [S], Object.getOwnPropertyDescriptor(L.prototype, "ignoreSort"), L.prototype),
                        G = L)) || G) || G) || G) || G));
            s._RF.pop()
        }
    }
}
));