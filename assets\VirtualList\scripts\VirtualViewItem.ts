import { _decorator, Component, Label, resources, Sprite, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

export class VirtualViewItemData {
    constructor(
        public index: number,
        public data: any,
        public type: string
    ) {}
}

interface ItemData {
    name?: string;
    info?: string;
    avatar?: string;
    icon?: string;
    [key: string]: any;
}

@ccclass('VirtualViewItem')
export class VirtualViewItem extends Component {
    @property(Label)
    labContent: Label | null = null;

    @property(Sprite)
    spIcon: Sprite | null = null;

    @property(Sprite)
    spAvatar: Sprite | null = null;

    @property(Label)
    labName: Label | null = null;

    public Init(data: VirtualViewItemData): void {
        this.Refresh(data);
    }

    public Refresh(data: VirtualViewItemData): void {
        const { data: itemData } = data;

        if (this.labName) {
            this.labName.string = itemData.name || '';
        }

        if (this.labContent) {
            this.labContent.string = itemData.info || '';
        }

        if (this.spAvatar && itemData?.avatar) {
            this.LoadImage(`Image/${itemData.avatar}/spriteFrame`).then(spriteFrame => {
                if (this.spAvatar) {
                    this.spAvatar.spriteFrame = spriteFrame;
                }
            });
        }

        if (this.spIcon && itemData?.icon) {
            this.LoadImage(`Image/${itemData.icon}/spriteFrame`).then(spriteFrame => {
                if (this.spIcon) {
                    this.spIcon.spriteFrame = spriteFrame;
                }
            });
        }
    }

    private LoadImage(path: string): Promise<SpriteFrame> {
        return new Promise((resolve, reject) => {
            resources.load(path, SpriteFrame, (err, spriteFrame) => {
                if (err) {
                    console.error('Failed to load spriteFrame:', err);
                    reject(err);
                } else {
                    resolve(spriteFrame);
                }
            });
        });
    }
} 